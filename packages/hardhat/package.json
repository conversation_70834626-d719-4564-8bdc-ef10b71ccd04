{"name": "hardhat", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"compile": "npx hardhat compile", "test": "npx hardhat test", "deploy:avax": "npx hardhat run ./ignition/modules/deploy.js --network avalancheFujiTestnet", "deploy:normal": "npx hardhat run ./ignition/modules/deploy.js --network sepolia", "hardhat-verify": "npx hardhat verify"}, "devDependencies": {"@hashgraph/sdk": "^2.61.0", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "dotenv": "^16.4.7", "ethers": "^6.13.4", "hardhat": "file:"}, "dependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-ignition": "^0.15.0", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@nomicfoundation/ignition-core": "^0.15.0", "@openzeppelin/contracts": "^4.9.3", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "chai": "^4.2.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.1", "ts-node": ">=8.0.0", "typechain": "^8.3.0", "typescript": ">=4.5.0"}}