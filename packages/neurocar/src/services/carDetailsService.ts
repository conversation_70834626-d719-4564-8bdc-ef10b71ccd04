import { supabase } from '@/utils/supabase';
import { CarDetailsWeb2, MarketplaceListing, CarImage, Offer } from '@/types/supabase';

export class CarDetailsService {
  // Check if Supabase is properly configured
  private static isSupabaseConfigured(): boolean {
    try {
      return !!supabase && !!process.env.NEXT_PUBLIC_SUPABASE_URL;
    } catch {
      return false;
    }
  }
  // Car Details Web2 Operations
  static async createCarDetails(carDetails: Omit<CarDetailsWeb2, 'id' | 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('car_details_web2')
        .insert(carDetails)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating car details:', error);
      return { data: null, error };
    }
  }

  static async getCarDetails(tokenId: string) {
    try {
      const { data, error } = await supabase
        .from('car_details_web2')
        .select('*')
        .eq('token_id', tokenId)
        .single();

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No data found - this is normal for new cars
          return { data: null, error: null };
        }
        if (error.code === '42P01') {
          // Table doesn't exist - return empty data instead of error
          console.warn('car_details_web2 table does not exist. Please run Supabase migrations.');
          return { data: null, error: null };
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching car details:', error);
      return { data: null, error: null }; // Return null error to prevent UI crashes
    }
  }

  static async updateCarDetails(tokenId: string, updates: Partial<CarDetailsWeb2>) {
    try {
      // Use upsert for cleaner insert-or-update functionality
      const { data, error } = await supabase
        .from('car_details_web2')
        .upsert({ 
          token_id: tokenId, 
          ...updates
        }, {
          onConflict: 'token_id', // Use token_id as the conflict resolution column
          ignoreDuplicates: false // Update on conflict
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating car details:', error);
      return { data: null, error };
    }
  }

  static async deleteCarDetails(tokenId: string) {
    try {
      const { error } = await supabase
        .from('car_details_web2')
        .delete()
        .eq('token_id', tokenId);

      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error('Error deleting car details:', error);
      return { error };
    }
  }

  // Car Images Operations
  static async addCarImage(carImage: Omit<CarImage, 'id' | 'created_at'>) {
    try {
      const { data, error } = await supabase
        .from('car_images')
        .insert(carImage)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error adding car image:', error);
      return { data: null, error };
    }
  }

  static async getCarImages(tokenId: string) {
    try {
      const { data, error } = await supabase
        .from('car_images')
        .select('*')
        .eq('token_id', tokenId)
        .order('is_primary', { ascending: false })
        .order('created_at', { ascending: true });

      if (error) {
        if (error.code === '42P01') {
          // Table doesn't exist - return empty array
          console.warn('car_images table does not exist. Please run Supabase migrations.');
          return { data: [], error: null };
        }
        throw error;
      }
      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching car images:', error);
      return { data: [], error: null }; // Return null error to prevent UI crashes
    }
  }

  static async updateCarImage(imageId: string, updates: Partial<CarImage>) {
    try {
      const { data, error } = await supabase
        .from('car_images')
        .update(updates)
        .eq('id', imageId)
        .select()
        .maybeSingle(); // Use maybeSingle() to handle 0 or 1 row

      if (error) throw error;
      
      // If no data returned, it means the image with this ID was not found
      if (!data) {
        return { data: null, error: new Error('Image not found') };
      }
      
      return { data, error: null };
    } catch (error) {
      console.error('Error updating car image:', error);
      return { data: null, error };
    }
  }

  static async deleteCarImage(imageId: string) {
    try {
      const { error } = await supabase
        .from('car_images')
        .delete()
        .eq('id', imageId);

      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error('Error deleting car image:', error);
      return { error };
    }
  }

  // Marketplace Listings Operations
  static async createMarketplaceListing(listing: Omit<MarketplaceListing, 'id' | 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .insert(listing)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating marketplace listing:', error);
      return { data: null, error };
    }
  }

  static async getMarketplaceListing(tokenId: string) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .select('*')
        .eq('token_id', tokenId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No data found - this is normal
          return { data: null, error: null };
        }
        if (error.code === '42P01') {
          // Table doesn't exist - return empty data
          console.warn('marketplace_listings table does not exist. Please run Supabase migrations.');
          return { data: null, error: null };
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching marketplace listing:', error);
      return { data: null, error: null }; // Return null error to prevent UI crashes
    }
  }

  static async updateMarketplaceListing(tokenId: string, updates: Partial<MarketplaceListing>) {
    try {
      // Use upsert for marketplace listings as well
      const { data, error } = await supabase
        .from('marketplace_listings')
        .upsert({ 
          token_id: tokenId, 
          ...updates 
        }, {
          onConflict: 'token_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating marketplace listing:', error);
      return { data: null, error };
    }
  }

  static async getActiveListings(limit: number = 20, offset: number = 0) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .select('*')
        .eq('is_active', true)
        .order('featured', { ascending: false })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching active listings:', error);
      return { data: [], error };
    }
  }

  static async incrementViews(tokenId: string) {
    try {
      const { error } = await supabase.rpc('increment_listing_views', {
        listing_token_id: tokenId
      });

      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error('Error incrementing views:', error);
      return { error };
    }
  }

  // Offers Operations
  static async createOffer(offer: Omit<Offer, 'id' | 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('offers')
        .insert(offer)
        .select()
        .single();

      if (error) {
        if (error.code === '42P01') {
          console.warn('offers table does not exist. Please run Supabase migrations.');
          return { data: null, error: null };
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error creating offer:', error);
      return { data: null, error: null };
    }
  }

  static async getOffersForToken(tokenId: string) {
    try {
      const { data, error } = await supabase
        .from('offers')
        .select('*')
        .eq('token_id', tokenId)
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === '42P01') {
          console.warn('offers table does not exist. Please run Supabase migrations.');
          return { data: [], error: null };
        }
        throw error;
      }
      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching offers:', error);
      return { data: [], error: null };
    }
  }

  static async getOffersForBuyer(buyerAddress: string) {
    try {
      const { data, error } = await supabase
        .from('offers')
        .select('*')
        .eq('buyer_address', buyerAddress)
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === '42P01') {
          console.warn('offers table does not exist. Please run Supabase migrations.');
          return { data: [], error: null };
        }
        throw error;
      }
      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching buyer offers:', error);
      return { data: [], error: null };
    }
  }

  static async getOffersForSeller(sellerAddress: string) {
    try {
      const { data, error } = await supabase
        .from('offers')
        .select('*')
        .eq('seller_address', sellerAddress)
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === '42P01') {
          console.warn('offers table does not exist. Please run Supabase migrations.');
          return { data: [], error: null };
        }
        throw error;
      }
      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching seller offers:', error);
      return { data: [], error: null };
    }
  }

  static async updateOfferStatus(offerId: string, status: Offer['status']) {
    try {
      const { data, error } = await supabase
        .from('offers')
        .update({ status })
        .eq('id', offerId)
        .select()
        .maybeSingle(); // Use maybeSingle() to handle 0 or 1 row

      if (error) {
        if (error.code === '42P01') {
          console.warn('offers table does not exist. Please run Supabase migrations.');
          return { data: null, error: null };
        }
        throw error;
      }
      
      // If no data returned, it means the offer with this ID was not found
      if (!data) {
        return { data: null, error: new Error('Offer not found') };
      }
      
      return { data, error: null };
    } catch (error) {
      console.error('Error updating offer status:', error);
      return { data: null, error: null };
    }
  }

  // Combined operations for enhanced car details
  static async getEnhancedCarDetails(tokenId: string) {
    try {
      const [detailsResult, imagesResult, listingResult, offersResult] = await Promise.all([
        this.getCarDetails(tokenId),
        this.getCarImages(tokenId),
        this.getMarketplaceListing(tokenId),
        this.getOffersForToken(tokenId)
      ]);

      return {
        details: detailsResult.data,
        images: imagesResult.data,
        listing: listingResult.data,
        offers: offersResult.data,
        error: detailsResult.error || imagesResult.error || listingResult.error || offersResult.error
      };
    } catch (error) {
      console.error('Error fetching enhanced car details:', error);
      return {
        details: null,
        images: [],
        listing: null,
        offers: [],
        error
      };
    }
  }
}
