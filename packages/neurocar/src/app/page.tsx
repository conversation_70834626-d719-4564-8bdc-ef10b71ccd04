"use client";

import { ConnectButton } from "thirdweb/react";
import { client, wallets, avalancheFuji } from "@/app/client";
import Link from "next/link";
import Footer from "@/components/Footer";

export default function Home() {
  return (
    <div className="min-h-screen bg-slate-50 text-slate-900 overflow-hidden">
      {/* Cyberpunk Grid Background */}
      <div className="fixed inset-0 z-0 opacity-10">
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </div>

      {/* Hero Section */}
      <div className="relative min-h-screen overflow-hidden">
        {/* Animated Glitch Effect */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400/20 to-transparent"></div>
          <div className="absolute bottom-0 right-0 w-3/4 h-3/4 bg-gradient-to-tl from-cyan-400/20 to-transparent"></div>
          <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-blue-500/30 blur-3xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 rounded-full bg-cyan-500/20 blur-3xl"></div>
        </div>

        {/* Digital Circuit Lines */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          <svg
            className="absolute top-0 left-0 w-full h-full opacity-10"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <path
              d="M0,0 L100,0 L100,100 L0,100 Z"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M0,20 L100,20"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M0,40 L100,40"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M0,60 L100,60"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M0,80 L100,80"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M20,0 L20,100"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M40,0 L40,100"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M60,0 L60,100"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
            <path
              d="M80,0 L80,100"
              fill="none"
              stroke="#4299e1"
              strokeWidth="0.1"
            />
          </svg>
        </div>

        <div className="container mx-auto px-6 relative z-10 flex flex-col justify-center h-screen">
          <div className="max-w-3xl">
            <div className="inline-block mb-4 px-4 py-1 bg-blue-100 border-l-4 border-blue-500 text-blue-700 font-mono">
              NEURO_CAR::SYSTEM_ONLINE
            </div>
            <h1 className="text-6xl font-bold leading-tight mb-6 glitch-text">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-400">
                Neural Vehicle Interface
              </span>
            </h1>
            <p className="text-xl mb-8 font-light text-slate-700 max-w-2xl">
              NeuroCar provides a secure, transparent digital logbook for your
              vehicle with
              <span className="text-blue-600 font-semibold">
                {" "}
                blockchain technology
              </span>
              . Manage maintenance records, verify history, and access
              decentralized marketplace.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                href="/dashboard"
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white font-mono rounded-md transition duration-300 text-center relative overflow-hidden group"
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                <span className="relative z-10">EXPLORE_DASHBOARD</span>
              </Link>
              <Link
                href="/mymech"
                className="px-8 py-4 bg-slate-100 border-2 border-blue-500 hover:bg-blue-50 text-blue-600 font-mono rounded-md transition duration-300 text-center"
              >
                DIGITAL_MECHANIC
              </Link>
            </div>
          </div>
        </div>

        {/* Animated Down Arrow */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10 animate-bounce">
          <svg
            className="w-8 h-8 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            ></path>
          </svg>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-white relative">
        <div className="absolute inset-0 bg-grid-blue-pattern opacity-5"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-block px-3 py-1 bg-blue-100 text-blue-600 font-mono text-sm mb-3">
              SYSTEM_FEATURES
            </div>
            <h2 className="text-4xl font-bold text-slate-800">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-500">
                Why Choose NeuroCar?
              </span>
            </h2>
            <p className="text-slate-600 mt-2 max-w-2xl mx-auto">
              Comprehensive vehicle management in the blockchain era
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-12">
            {/* Feature 1 */}
            <div className="bg-slate-50 rounded-lg p-8 hover:shadow-lg transition duration-300 border border-slate-200 hover:border-blue-300 group">
              <div className="bg-blue-100 p-3 rounded-lg w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-blue-500 group-hover:text-white transition-colors">
                <svg
                  className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  ></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2 font-mono">
                IMMUTABLE_RECORDS
              </h3>
              <p className="text-slate-600">
                All maintenance records, issue reports, and history are securely
                stored on the blockchain, ensuring transparency and preventing
                fraud.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-slate-50 rounded-lg p-8 hover:shadow-lg transition duration-300 border border-slate-200 hover:border-blue-300 group">
              <div className="bg-blue-100 p-3 rounded-lg w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-blue-500 group-hover:text-white transition-colors">
                <svg
                  className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  ></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2 font-mono">
                TOKENIZED_OWNERSHIP
              </h3>
              <p className="text-slate-600">
                Your vehicle is represented as an NFT, providing proof of
                ownership, simplified transfers, and increased resale value
                through verified history.
              </p>
            </div>

                        {/* Feature 3 */}
            <div className="bg-slate-50 rounded-lg p-8 hover:shadow-lg transition duration-300 border border-slate-200 hover:border-blue-300 group">
              <div className="bg-blue-100 p-3 rounded-lg w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-blue-500 group-hover:text-white transition-colors">
                <svg
                  className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                  ></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2 font-mono">
                DECENTRALIZED_MARKETPLACE
              </h3>
              <p className="text-slate-600">
                Access a marketplace for vehicles, list your vehicle NFT to sell and also explore to buy vehicles with transparent records.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="py-20 bg-slate-100 relative">
        <div className="absolute inset-0 bg-circuit-pattern opacity-5"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-block px-3 py-1 bg-blue-100 text-blue-600 font-mono text-sm mb-3">
              SYSTEM_PROTOCOL
            </div>
            <h2 className="text-4xl font-bold text-slate-800">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-500">
                How It Works
              </span>
            </h2>
            <p className="text-slate-600 mt-2">
              Simple steps to digitize your vehicle on the blockchain
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="bg-gradient-to-r from-blue-600 to-blue-400 text-white w-14 h-14 rounded-lg flex items-center justify-center mx-auto mb-6 text-xl font-bold relative overflow-hidden">
                  <div className="absolute inset-0 bg-blue-600 animate-pulse opacity-50"></div>
                  <span className="relative z-10">{step}</span>
                </div>
                {index < 3 && (
                  <div className="hidden md:block absolute top-7 left-1/2 w-full h-0.5 bg-gradient-to-r from-blue-500 to-blue-300"></div>
                )}
                <h3 className="text-xl font-semibold mb-2 font-mono">
                  {index === 0 && "SIGN IN"}
                  {index === 1 && "MINT_CAR_NFT"}
                  {index === 2 && "RECORD_MAINTENANCE"}
                  {index === 3 && "RUN DIAGNOSTICS"}
                </h3>
                <p className="text-slate-600">
                  {index === 0 &&
                    "Sign In to get started with NeuroCar"}
                  {index === 1 &&
                    "Register your vehicle details and create a unique NFT"}
                  {index === 2 &&
                    "Add service records, repairs, and issue reports"}
                  {index === 3 &&
                    "Use our AI diagnostics to analyze vehicle health"}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-blue-600 to-blue-500 text-white relative overflow-hidden">
        <div className="absolute inset-0">
          <svg
            className="absolute inset-0 h-full w-full"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <pattern
                id="grid-pattern"
                width="40"
                height="40"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 40 0 L 0 0 0 40"
                  fill="none"
                  stroke="rgba(255,255,255,0.1)"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid-pattern)" />
          </svg>
        </div>
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="inline-block px-3 py-1 bg-white/20 text-white font-mono text-sm mb-3 backdrop-blur-sm">
            SYSTEM_INVITATION
          </div>
          <h2 className="text-4xl font-bold mb-4">
            Ready to Revolutionize Your Vehicle Ownership?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of vehicle owners who have already digitized their
            vehicles with NeuroCar&apos;s blockchain technology.
          </p>          <div className="inline-block backdrop-blur-sm bg-white/10 p-2 rounded-lg">
            <ConnectButton
              client={client}
              wallets={wallets}
              theme={"light"}
              chain={avalancheFuji}
              connectButton={{
                label: "Connect to Neurocar",
              }}
              connectModal={{
                size: "wide",
                title: "Sign In to Neurocar",
                titleIcon: "https://res.cloudinary.com/dzwwxzfky/image/upload/t_Profile/v1749562360/nuero_qxusis.jpg",
                termsOfServiceUrl: "./terms",
                privacyPolicyUrl: "./privacy",
                // requireApproval: true,
                // showThirdwebBranding: false,
              }}
            />
          </div>
        </div>      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
