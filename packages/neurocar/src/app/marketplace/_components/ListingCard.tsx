"use client";

import React, { useState } from "react";
import { formatUnits } from "viem";
import Image from "next/image";
import Link from "next/link";
import {
  Listing,
} from "@/blockchain/hooks/useCarMarketplaceHybrid";
import { useThirdwebWagmiAccount } from "@/blockchain/hooks/useThirdwebWagmiAdapter";
// import { useApproveMarketplace } from "@/blockchain/hooks/useCarMarketplace"; // Import NFT approval hook



interface ListingCardProps {
  listing: Listing;
}

const ListingCard: React.FC<ListingCardProps> = ({
  listing,
}) => {
  const { address: connectedAddress, isConnected } = useThirdwebWagmiAccount();
  const [statusMessage, setStatusMessage] = useState<{
    type: "info" | "error" | "success";
    message: string;
  } | null>(null);

  const { carDetails, price, seller, tokenId } = listing;
  const priceInUSDT = formatUnits(price, 6); // Assuming 6 decimals for USDT

  const isOwner =
    isConnected && connectedAddress?.toLowerCase() === seller?.toLowerCase();







  // --- Event Handlers ---





  const clearStatus = () => {
    setStatusMessage(null);
  };

  // --- Render Button Logic ---
  const renderActionButton = () => {
    const disabledButtonClasses = "bg-gray-400 cursor-not-allowed";
    const greenButtonClasses = "bg-green-600 hover:bg-green-700";

    if (!isConnected) {
      return (
        <button
          disabled
          className={`w-full px-4 py-2 rounded text-white transition duration-200 flex items-center justify-center text-sm font-medium ${disabledButtonClasses}`}
        >
          Connect Wallet to Interact
        </button>
      );
    }

    // Owner Actions - Show View Details instead of Cancel Listing
    if (isOwner) {
      return (
        <Link
          href={`/car/${tokenId}`}
          className={`w-full px-4 py-2 rounded text-white transition duration-200 flex items-center justify-center text-sm font-medium ${greenButtonClasses} text-center no-underline`}
        >
          View Details
        </Link>
      );
    }

    // Buyer Actions - Show Buy Now button
    return (
      <Link
        href={`/car/${tokenId}`}
        className={`w-full px-4 py-2 rounded text-white transition duration-200 flex items-center justify-center text-sm font-medium ${greenButtonClasses} text-center no-underline`}
      >
        Buy Now
      </Link>
    );
  };

  // --- Status Message Display ---
  const getStatusIcon = () => {
    if (!statusMessage) return null;
    switch (statusMessage.type) {
      case "info":
        return <span className="mr-1">ℹ️</span>;
      case "success":
        return <span className="mr-1">✅</span>;
      case "error":
        return <span className="mr-1">❌</span>;
      default:
        return null;
    }
  };

  const getAlertClasses = () => {
    if (!statusMessage) return "";
    switch (statusMessage.type) {
      case "info":
        return "bg-blue-100 border-blue-500 text-blue-700";
      case "success":
        return "bg-green-100 border-green-500 text-green-700";
      case "error":
        return "bg-red-100 border-red-500 text-red-700";
      default:
        return "bg-gray-100 border-gray-500 text-gray-700";
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 flex flex-col">
      <div className="h-48 bg-gray-200 flex items-center justify-center text-gray-500 relative">
        {carDetails?.imageURI ? (
          <Image
            src={carDetails.imageURI}
            alt={`${carDetails.make} ${carDetails.model}`}
            fill
            sizes="(max-width: 768px) 100vw, 384px"
            className="object-cover"
            priority
          />
        ) : (
          <span>No Image</span>
        )}
      </div>

      <div className="p-4 flex-grow flex flex-col">
        {carDetails ? ( // Check if carDetails is available
          <h3 className="text-lg font-semibold text-slate-800 mb-1">
            {carDetails.year?.toString()} {carDetails.make} {carDetails.model}
          </h3>
        ) : (
          <h3 className="text-lg font-semibold text-slate-800 mb-1">
            Car Details Unavailable
          </h3>
        )}
        <p className="text-sm text-slate-600 mb-1">
          Token ID: {tokenId.toString()}
        </p>
        <p className="text-sm text-slate-600 mb-3 truncate" title={seller}>
          Seller:{" "}
          {seller ? `${seller.slice(0, 6)}...${seller.slice(-4)}` : "N/A"}
        </p>

        <div className="mt-auto pt-3 border-t border-gray-100">
          <p className="text-xl font-bold text-slate-900 mb-3">
            {priceInUSDT}{" "}
            <span className="text-sm font-normal text-slate-500">USDT</span>
          </p>

          {/* Status Message Area */}
          {statusMessage && (
            <div
              className={`mb-3 p-2 border-l-4 rounded text-xs ${getAlertClasses()}`}
              role="alert"
            >
              <div className="flex items-center">
                {getStatusIcon()}
                <span className="font-bold mr-1">
                  {statusMessage.type.toUpperCase()}
                </span>
                <p className="flex-grow">{statusMessage.message}</p>
                <button
                  onClick={clearStatus}
                  className="ml-1 text-xs font-semibold hover:opacity-80"
                  aria-label="Dismiss message"
                >
                  &times;
                </button>
              </div>
            </div>
          )}

          {/* View Details Button */}
          {/* <Link
            href={`/car/${tokenId}`}
            className="block w-full mb-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 text-center transition-colors"
          >
            View Details
          </Link> */}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {renderActionButton()}
          </div>
        </div>
      </div>


    </div>
  );
};

export default ListingCard;
