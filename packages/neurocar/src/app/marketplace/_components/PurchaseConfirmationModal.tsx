"use client";

import React, { useState, useEffect } from "react";
import { formatUnits } from "viem";
import { CheckCircle, AlertTriangle, X, Loader, DollarSign } from "lucide-react";
import { Listing, useApproveUsdtHybrid } from "@/blockchain/hooks/useCarMarketplaceHybrid";

interface PurchaseConfirmationModalProps {
  listing: Listing;
  isOpen: boolean;
  onClose: () => void;
  onConfirmPurchase: () => void;
  isPurchasing: boolean;
}

const PurchaseConfirmationModal: React.FC<PurchaseConfirmationModalProps> = ({
  listing,
  isOpen,
  onClose,
  onConfirmPurchase,
  isPurchasing,
}) => {
  const [sellerAgreement, setSellerAgreement] = useState(false);
  const [buyerAgreement, setBuyerAgreement] = useState(false);
  const [inspectionConfirmed, setInspectionConfirmed] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  if (!isOpen) return null;

  const { carDetails, price } = listing;
  const priceInUSDT = formatUnits(price, 6);

  // USDT Approval Hook
  const {
    approveUsdt,
    needsApproval: needsUsdtApproval,
    isApproving: isApprovingUsdt,
    isConfirmingApproval: isConfirmingUsdtApproval,
    isApprovalConfirmed: isUsdtApprovalConfirmed,
    approvalError: usdtApprovalError,
    clearApprovalError: clearUsdtApprovalError,
    isLoadingAllowanceStatus: isLoadingUsdtAllowance,
  } = useApproveUsdtHybrid(price);

  const allConditionsMet = sellerAgreement && buyerAgreement && inspectionConfirmed && termsAccepted;
  const canProceedToPurchase = allConditionsMet && !needsUsdtApproval;

  const handleApproveUsdt = async () => {
    clearUsdtApprovalError();
    await approveUsdt();
  };

  const handleConfirm = () => {
    if (canProceedToPurchase) {
      onConfirmPurchase();
    }
  };

  const resetForm = () => {
    setSellerAgreement(false);
    setBuyerAgreement(false);
    setInspectionConfirmed(false);
    setTermsAccepted(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">
            Purchase Confirmation
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isPurchasing}
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Vehicle Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Vehicle Details</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Make & Model:</span>
                <span className="ml-2 font-medium">{carDetails.make} {carDetails.model}</span>
              </div>
              <div>
                <span className="text-gray-600">Year:</span>
                <span className="ml-2 font-medium">{carDetails.year.toString()}</span>
              </div>
              <div>
                <span className="text-gray-600">VIN:</span>
                <span className="ml-2 font-medium">{carDetails.vin}</span>
              </div>
              <div>
                <span className="text-gray-600">Price:</span>
                <span className="ml-2 font-medium text-green-600">{priceInUSDT} USDT</span>
              </div>
            </div>
          </div>

          {/* USDT Approval Section */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center">
              <DollarSign size={20} className="mr-2 text-blue-600" />
              USDT Payment Approval
            </h3>

            {isLoadingUsdtAllowance ? (
              <div className="flex items-center text-sm text-gray-600">
                <Loader size={16} className="animate-spin mr-2" />
                Checking USDT allowance...
              </div>
            ) : needsUsdtApproval ? (
              <div className="space-y-3">
                <p className="text-sm text-gray-700">
                  You need to approve USDT spending before you can purchase this vehicle.
                  This allows the marketplace contract to transfer {priceInUSDT} USDT from your wallet.
                </p>
                {usdtApprovalError && (
                  <div className="bg-red-50 border border-red-200 rounded p-2 text-sm text-red-700">
                    <AlertTriangle size={16} className="inline mr-1" />
                    {usdtApprovalError.message}
                  </div>
                )}
                <button
                  onClick={handleApproveUsdt}
                  disabled={isApprovingUsdt || isConfirmingUsdtApproval || isPurchasing}
                  className={`w-full px-4 py-2 rounded-md text-sm font-medium text-white ${
                    isApprovingUsdt || isConfirmingUsdtApproval || isPurchasing
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-blue-600 hover:bg-blue-700"
                  }`}
                >
                  {(isApprovingUsdt || isConfirmingUsdtApproval) ? (
                    <>
                      <Loader size={16} className="inline animate-spin mr-2" />
                      {isApprovingUsdt ? "Approving..." : "Confirming..."}
                    </>
                  ) : (
                    <>
                      <DollarSign size={16} className="inline mr-2" />
                      Approve {priceInUSDT} USDT
                    </>
                  )}
                </button>
              </div>
            ) : (
              <div className="flex items-center text-sm text-green-700">
                <CheckCircle size={16} className="mr-2" />
                USDT spending approved - ready to purchase
              </div>
            )}
          </div>

          {/* Satisfaction Confirmations */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900">Pre-Purchase Confirmations</h3>
            
            <div className="space-y-3">
              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={inspectionConfirmed}
                  onChange={(e) => setInspectionConfirmed(e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isPurchasing}
                />
                <div className="text-sm">
                  <span className="font-medium text-gray-900">Vehicle Inspection Completed</span>
                  <p className="text-gray-600">I have thoroughly inspected the vehicle and am satisfied with its condition.</p>
                </div>
              </label>

              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={buyerAgreement}
                  onChange={(e) => setBuyerAgreement(e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isPurchasing}
                />
                <div className="text-sm">
                  <span className="font-medium text-gray-900">Buyer Agreement</span>
                  <p className="text-gray-600">I confirm that I want to purchase this vehicle at the listed price and understand this transaction is final.</p>
                </div>
              </label>

              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={sellerAgreement}
                  onChange={(e) => setSellerAgreement(e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isPurchasing}
                />
                <div className="text-sm">
                  <span className="font-medium text-gray-900">Seller Agreement (Assumed)</span>
                  <p className="text-gray-600">By listing this vehicle, the seller agrees to transfer ownership upon payment.</p>
                </div>
              </label>

              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isPurchasing}
                />
                <div className="text-sm">
                  <span className="font-medium text-gray-900">Terms & Conditions</span>
                  <p className="text-gray-600">I accept the marketplace terms and conditions and understand the risks of blockchain transactions.</p>
                </div>
              </label>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important Notice:</p>
                <p>This transaction is irreversible. Please ensure you have completed all necessary inspections and verifications before proceeding.</p>
                {needsUsdtApproval && allConditionsMet && (
                  <p className="mt-2 font-medium">
                    ⚠️ You must approve USDT spending before you can complete the purchase.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={handleClose}
            disabled={isPurchasing}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={!canProceedToPurchase || isPurchasing}
            className={`px-4 py-2 rounded-md text-sm font-medium text-white ${
              canProceedToPurchase && !isPurchasing
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-400 cursor-not-allowed"
            }`}
          >
            {isPurchasing ? (
              <>
                <Loader size={16} className="inline animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                <CheckCircle size={16} className="inline mr-2" />
                Confirm Purchase
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PurchaseConfirmationModal;
