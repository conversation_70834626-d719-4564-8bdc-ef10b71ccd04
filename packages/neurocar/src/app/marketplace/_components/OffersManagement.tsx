"use client";

import React, { useState, useEffect, useCallback } from "react";
import { 
  Clock, 
  DollarSign, 
  MessageSquare, 
  Check, 
  X, 
  Eye,
  Calendar,
  User
} from "lucide-react";
import { CarDetailsService } from "@/services/carDetailsService";
import { Offer } from "@/types/supabase";
import { useThirdwebWagmiAccount } from "@/blockchain/hooks/useThirdwebWagmiAdapter";
import { formatDistanceToNow } from "date-fns";

interface OffersManagementProps {
  tokenId: string;
  sellerAddress: string;
  isOwner: boolean;
}

const OffersManagement: React.FC<OffersManagementProps> = ({
  tokenId,
  sellerAddress: _sellerAddress, // Currently unused but kept for future functionality
  isOwner,
}) => {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  
  // Acknowledge unused parameter to avoid ESLint warning
  void _sellerAddress;

  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [showOfferDetails, setShowOfferDetails] = useState(false);

  const fetchOffers = useCallback(async () => {
    setLoading(true);
    try {
      const result = await CarDetailsService.getOffersForToken(tokenId);
      if (result.data) {
        // Filter offers based on user role
        let filteredOffers = result.data;
        if (!isOwner && connectedAddress) {
          // If not owner, only show offers from this buyer
          filteredOffers = result.data.filter(
            offer => offer.buyer_address.toLowerCase() === connectedAddress.toLowerCase()
          );
        }
        setOffers(filteredOffers);
      }
    } catch (error) {
      console.error("Error fetching offers:", error);
    } finally {
      setLoading(false);
    }
  }, [tokenId, isOwner, connectedAddress]);

  useEffect(() => {
    fetchOffers();
  }, [fetchOffers]);

  const handleAcceptOffer = async (offerId: string) => {
    if (!confirm("Are you sure you want to accept this offer? This action cannot be undone.")) {
      return;
    }

    try {
      const result = await CarDetailsService.updateOfferStatus(offerId, 'accepted');
      if (result.data) {
        alert("Offer accepted! The buyer will be notified to complete the transaction.");
        fetchOffers();
      } else {
        alert("Failed to accept offer. Please try again.");
      }
    } catch (error) {
      console.error("Error accepting offer:", error);
      alert("Failed to accept offer. Please try again.");
    }
  };

  const handleRejectOffer = async (offerId: string) => {
    if (!confirm("Are you sure you want to reject this offer?")) {
      return;
    }

    try {
      const result = await CarDetailsService.updateOfferStatus(offerId, 'rejected');
      if (result.data) {
        alert("Offer rejected.");
        fetchOffers();
      } else {
        alert("Failed to reject offer. Please try again.");
      }
    } catch (error) {
      console.error("Error rejecting offer:", error);
      alert("Failed to reject offer. Please try again.");
    }
  };

  const handleWithdrawOffer = async (offerId: string) => {
    if (!confirm("Are you sure you want to withdraw this offer?")) {
      return;
    }

    try {
      const result = await CarDetailsService.updateOfferStatus(offerId, 'withdrawn');
      if (result.data) {
        alert("Offer withdrawn.");
        fetchOffers();
      } else {
        alert("Failed to withdraw offer. Please try again.");
      }
    } catch (error) {
      console.error("Error withdrawing offer:", error);
      alert("Failed to withdraw offer. Please try again.");
    }
  };

  const getStatusColor = (status: Offer['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isOfferExpired = (expiresAt?: string) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  if (loading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-600">Loading offers...</p>
      </div>
    );
  }

  if (offers.length === 0) {
    return (
      <div className="p-6 text-center">
        <DollarSign size={48} className="mx-auto text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Offers Yet</h3>
        <p className="text-gray-600">
          {isOwner 
            ? "No offers have been made for this vehicle yet."
            : "You haven't made any offers for this vehicle yet."
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">
          {isOwner ? "Received Offers" : "Your Offers"} ({offers.length})
        </h3>
      </div>

      <div className="space-y-3">
        {offers.map((offer) => (
          <div
            key={offer.id}
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <span className="text-2xl font-bold text-green-600">
                    {offer.offer_amount} USDT
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(offer.status)}`}>
                    {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                  </span>
                  {isOfferExpired(offer.expires_at) && offer.status === 'pending' && (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Expired
                    </span>
                  )}
                </div>

                <div className="flex items-center text-sm text-gray-600 space-x-4">
                  <div className="flex items-center">
                    <User size={14} className="mr-1" />
                    <span>
                      {isOwner 
                        ? `From: ${offer.buyer_address.slice(0, 6)}...${offer.buyer_address.slice(-4)}`
                        : `To: ${offer.seller_address.slice(0, 6)}...${offer.seller_address.slice(-4)}`
                      }
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    <span>{formatDistanceToNow(new Date(offer.created_at!), { addSuffix: true })}</span>
                  </div>
                  {offer.expires_at && (
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      <span>
                        Expires {formatDistanceToNow(new Date(offer.expires_at), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </div>

                {offer.message && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-700">
                    <MessageSquare size={14} className="inline mr-1" />
                    {offer.message}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => {
                    setSelectedOffer(offer);
                    setShowOfferDetails(true);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="View details"
                >
                  <Eye size={16} />
                </button>

                {/* Action buttons based on user role and offer status */}
                {isOwner && offer.status === 'pending' && !isOfferExpired(offer.expires_at) && (
                  <>
                    <button
                      onClick={() => handleAcceptOffer(offer.id!)}
                      className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                      title="Accept offer"
                    >
                      <Check size={14} className="inline mr-1" />
                      Accept
                    </button>
                    <button
                      onClick={() => handleRejectOffer(offer.id!)}
                      className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                      title="Reject offer"
                    >
                      <X size={14} className="inline mr-1" />
                      Reject
                    </button>
                  </>
                )}

                {!isOwner && offer.status === 'pending' && !isOfferExpired(offer.expires_at) && (
                  <button
                    onClick={() => handleWithdrawOffer(offer.id!)}
                    className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                    title="Withdraw offer"
                  >
                    Withdraw
                  </button>
                )}

                {offer.status === 'accepted' && (
                  <div className="text-sm text-green-600 font-medium">
                    Ready to complete transaction
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Offer Details Modal */}
      {showOfferDetails && selectedOffer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Offer Details</h3>
              <button
                onClick={() => setShowOfferDetails(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Amount</label>
                <p className="text-2xl font-bold text-green-600">{selectedOffer.offer_amount} USDT</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedOffer.status)}`}>
                  {selectedOffer.status.charAt(0).toUpperCase() + selectedOffer.status.slice(1)}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {isOwner ? "Buyer" : "Seller"}
                </label>
                <p className="text-sm text-gray-900 font-mono">
                  {isOwner ? selectedOffer.buyer_address : selectedOffer.seller_address}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="text-sm text-gray-900">
                  {new Date(selectedOffer.created_at!).toLocaleString()}
                </p>
              </div>

              {selectedOffer.expires_at && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Expires</label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedOffer.expires_at).toLocaleString()}
                  </p>
                </div>
              )}

              {selectedOffer.message && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Message</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                    {selectedOffer.message}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OffersManagement;
