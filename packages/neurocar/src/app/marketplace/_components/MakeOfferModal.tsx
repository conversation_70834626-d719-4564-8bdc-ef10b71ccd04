"use client";

import React, { useState } from "react";
import { X, DollarSign, MessageSquare, Calendar, Send } from "lucide-react";
import { CarDetailsService } from "@/services/carDetailsService";
import { useThirdwebWagmiAccount } from "@/blockchain/hooks/useThirdwebWagmiAdapter";

interface MakeOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  tokenId: string;
  sellerAddress: string;
  currentPrice?: string;
  carInfo: {
    make: string;
    model: string;
    year: string;
  };
}

const MakeOfferModal: React.FC<MakeOfferModalProps> = ({
  isOpen,
  onClose,
  tokenId,
  sellerAddress,
  currentPrice,
  carInfo,
}) => {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const [offerAmount, setOfferAmount] = useState("");
  const [message, setMessage] = useState("");
  const [expiryDays, setExpiryDays] = useState("7");
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!connectedAddress) {
      alert("Please connect your wallet to make an offer");
      return;
    }

    if (!offerAmount || parseFloat(offerAmount) <= 0) {
      alert("Please enter a valid offer amount");
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate expiry date
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + parseInt(expiryDays));

      const offerData = {
        token_id: tokenId,
        buyer_address: connectedAddress,
        seller_address: sellerAddress,
        offer_amount: offerAmount,
        message: message || "",
        status: 'pending' as const,
        expires_at: expiryDate.toISOString(),
      };

      const result = await CarDetailsService.createOffer(offerData);

      if (result.data) {
        alert("Offer submitted successfully!");
        onClose();
        // Reset form
        setOfferAmount("");
        setMessage("");
        setExpiryDays("7");
      } else {
        alert("Failed to submit offer. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting offer:", error);
      alert("Failed to submit offer. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">Make an Offer</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Vehicle Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Vehicle</h3>
            <p className="text-gray-700">
              {carInfo.year} {carInfo.make} {carInfo.model}
            </p>
            {currentPrice && (
              <p className="text-sm text-gray-600 mt-1">
                Listed Price: {currentPrice} USDT
              </p>
            )}
          </div>

          {/* Offer Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <DollarSign size={16} className="inline mr-1" />
              Offer Amount (USDT)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={offerAmount}
              onChange={(e) => setOfferAmount(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter your offer amount"
              required
              disabled={isSubmitting}
            />
            {currentPrice && offerAmount && (
              <p className="text-xs text-gray-500 mt-1">
                {parseFloat(offerAmount) < parseFloat(currentPrice) 
                  ? `${((parseFloat(currentPrice) - parseFloat(offerAmount)) / parseFloat(currentPrice) * 100).toFixed(1)}% below listed price`
                  : parseFloat(offerAmount) > parseFloat(currentPrice)
                  ? `${((parseFloat(offerAmount) - parseFloat(currentPrice)) / parseFloat(currentPrice) * 100).toFixed(1)}% above listed price`
                  : "Same as listed price"
                }
              </p>
            )}
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MessageSquare size={16} className="inline mr-1" />
              Message to Seller (Optional)
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Add a personal message or explain your offer..."
              disabled={isSubmitting}
            />
          </div>

          {/* Expiry */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar size={16} className="inline mr-1" />
              Offer Expires In
            </label>
            <select
              value={expiryDays}
              onChange={(e) => setExpiryDays(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              <option value="1">1 day</option>
              <option value="3">3 days</option>
              <option value="7">7 days</option>
              <option value="14">14 days</option>
              <option value="30">30 days</option>
            </select>
          </div>

          {/* Terms Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 mb-2">Important Notice</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Your offer is binding once accepted by the seller</li>
              <li>• You must have sufficient USDT balance to complete the purchase</li>
              <li>• The transaction will be processed through smart contracts</li>
              <li>• Both parties must agree before the NFT transfer occurs</li>
            </ul>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !offerAmount}
              className={`px-4 py-2 rounded-md text-sm font-medium text-white ${
                isSubmitting || !offerAmount
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <Send size={16} className="inline mr-2" />
                  Submit Offer
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MakeOfferModal;
