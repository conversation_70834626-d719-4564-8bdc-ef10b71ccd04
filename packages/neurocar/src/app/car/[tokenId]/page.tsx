"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowLeft,
  Calendar,
  MapPin,
  Heart,
  Share2,
  AlertTriangle,
  CheckCircle,
  Wrench,
  Shield,
  FileText,
  DollarSign
} from "lucide-react";
import { useCarDetails } from "@/blockchain/hooks/useCarNFTHybrid";
import { useCarDetailsWeb2 } from "@/hooks/useCarDetailsWeb2";
import { useListingDetails } from "@/blockchain/hooks/useCarMarketplaceHybrid";
import { formatUnits, type Address } from "viem";
import { useReadContract } from "wagmi";
import { carnft_address, carnft_abi } from "@/blockchain/abi/neuro";
import PurchaseConfirmationModal from "@/app/marketplace/_components/PurchaseConfirmationModal";
import MakeOfferModal from "@/app/marketplace/_components/MakeOfferModal";
import OffersManagement from "@/app/marketplace/_components/OffersManagement";
import { usePurchaseCarHybrid } from "@/blockchain/hooks/useCarMarketplaceHybrid";
import { useThirdwebWagmiAccount } from "@/blockchain/hooks/useThirdwebWagmiAdapter";

export default function CarDetailsPage(): React.ReactElement {
  const params = useParams();
  const router = useRouter();
  const tokenId = params.tokenId as string;
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showOfferModal, setShowOfferModal] = useState(false);

  // Fetch blockchain data
  const { data: carDetails, isLoading: carLoading, error: carError } = useCarDetails(BigInt(tokenId));
  const { details: listingDetails, isLoading: listingLoading } = useListingDetails(BigInt(tokenId));
  
  // Fetch the current owner using ownerOf function
  const { data: currentOwnerData, isLoading: ownerLoading } = useReadContract({
    address: carnft_address as Address,
    abi: carnft_abi,
    functionName: 'ownerOf',
    args: [BigInt(tokenId)],
    query: { enabled: !!tokenId }
  });

  // Type guard for currentOwner
  const currentOwner = currentOwnerData as string | undefined;
  
  // Fetch web2 data
  const {
    details: web2Details,
    images: additionalImages,
    loading: web2Loading
  } = useCarDetailsWeb2(tokenId);

  // Purchase functionality
  const { purchaseCar, isPurchasing } = usePurchaseCarHybrid();

  const isLoading = carLoading || listingLoading || web2Loading || ownerLoading;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading car details...</p>
        </div>
      </div>
    );
  }

  if (carError || !carDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Car Not Found</h1>
          <p className="text-gray-600 mb-4">The requested car could not be found.</p>
          <Link href="/marketplace" className="text-blue-600 hover:text-blue-800">
            ← Back to Marketplace
          </Link>
        </div>
      </div>
    );
  }

  // Ensure we have valid data before rendering
  if (!carDetails || !additionalImages) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading car details...</p>
        </div>
      </div>
    );
  }

  // Combine primary image with additional images
  const allImages = [
    { url: carDetails.imageURI, type: 'primary', description: 'Primary Image' },
    ...additionalImages.map(img => ({ 
      url: img.image_url, 
      type: img.image_type, 
      description: img.description || img.image_type 
    }))
  ];
  const currentImage = allImages[activeImageIndex];
  const isForSale = listingDetails?.isActive;
  const price = listingDetails?.price ? formatUnits(listingDetails.price, 6) : null;
  const isOwner = connectedAddress?.toLowerCase() === currentOwner?.toLowerCase();

  // Debug logging
  console.log('Car Details Debug:', {
    tokenId,
    currentOwner,
    connectedAddress,
    isOwner,
    ownerLoading
  });

  const handlePurchase = async () => {
    if (!listingDetails) return;

    await purchaseCar({
      tokenId: BigInt(tokenId),
      onSuccess: (txHash) => {
        console.log("Purchase successful:", txHash);
        setShowPurchaseModal(false);
        // Show success message and redirect to marketplace
        alert("Purchase successful! The vehicle is now yours. Redirecting to marketplace...");
        router.push("/marketplace");
      },
      onError: (error) => {
        console.error("Purchase failed:", error);
        // Show user-friendly error message
        if (error.message.includes("allowance")) {
          alert("USDT approval required. Please approve USDT spending in the purchase modal and try again.");
        } else if (error.message.includes("balance")) {
          alert("Insufficient USDT balance. Please ensure you have enough USDT to complete the purchase.");
        } else {
          alert(`Purchase failed: ${error.message}`);
        }
      }
    });
  };

  const handleMakeOffer = () => {
    // Safety check: only open modal if we have owner information
    if (!currentOwner) {
      console.error('Cannot make offer: current owner not found');
      return;
    }
    setShowOfferModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/marketplace" 
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Marketplace
            </Link>
            <div className="flex items-center space-x-3">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                <Heart size={20} />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                <Share2 size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Images Section */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
              <Image
                src={currentImage.url.replace("ipfs://", "https://ipfs.io/ipfs/")}
                alt={currentImage.description}
                className="w-full h-full object-cover"
                width={600}
                height={400}
                priority
              />
            </div>
            
            {/* Thumbnail Images */}
            {allImages.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {allImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 border-2 rounded-lg overflow-hidden ${
                      index === activeImageIndex ? 'border-blue-500' : 'border-gray-200'
                    }`}
                  >
                    <Image
                      src={image.url.replace("ipfs://", "https://ipfs.io/ipfs/")}
                      alt={image.description}
                      className="w-full h-full object-cover"
                      width={150}
                      height={150}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Car Information */}
          <div className="space-y-6">
            {/* Basic Info */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {carDetails.make} {carDetails.model}
              </h1>
              <div className="flex items-center mt-2 text-gray-600">
                <Calendar size={16} className="mr-2" />
                <span>{carDetails.year.toString()}</span>
                <span className="mx-2">•</span>
                <span>VIN: {carDetails.vin}</span>
              </div>
              {web2Details?.location && (
                <div className="flex items-center mt-1 text-gray-600">
                  <MapPin size={16} className="mr-2" />
                  <span>{web2Details.location}</span>
                </div>
              )}
            </div>

            {/* Price and Actions */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              {isForSale && price ? (
                <div className="space-y-4">
                  <div>
                    <div className="text-3xl font-bold text-green-600">
                      ${parseFloat(price).toLocaleString()} USDT
                    </div>
                    <p className="text-sm text-gray-600">Listed price</p>
                  </div>
                  <div className="flex space-x-3">
                    {!isOwner && (
                      <>
                        <button
                          onClick={() => setShowPurchaseModal(true)}
                          className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700"
                        >
                          <DollarSign size={20} className="inline mr-2" />
                          Buy Now
                        </button>
                        <button
                          onClick={handleMakeOffer}
                          className="flex-1 bg-white text-blue-600 border border-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50"
                        >
                          Make Offer
                        </button>
                      </>
                    )}
                    {isOwner && (
                      <div className="w-full text-center py-3 bg-gray-100 rounded-lg text-gray-600">
                        You own this vehicle
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <div className="text-xl font-bold text-gray-900">Not For Sale</div>
                    <p className="text-sm text-gray-600">This vehicle is not currently listed</p>
                  </div>
                  {!isOwner && (
                    <button
                      onClick={handleMakeOffer}
                      className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700"
                    >
                      Make Offer
                    </button>
                  )}
                  {isOwner && (
                    <div className="w-full text-center py-3 bg-gray-100 rounded-lg text-gray-600">
                      You own this vehicle
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Vehicle Details */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Make:</span>
                  <span className="ml-2 font-medium">{carDetails.make}</span>
                </div>
                <div>
                  <span className="text-gray-600">Model:</span>
                  <span className="ml-2 font-medium">{carDetails.model}</span>
                </div>
                <div>
                  <span className="text-gray-600">Year:</span>
                  <span className="ml-2 font-medium">{carDetails.year.toString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">VIN:</span>
                  <span className="ml-2 font-medium text-xs">{carDetails.vin}</span>
                </div>
                <div>
                  <span className="text-gray-600">Registration:</span>
                  <span className="ml-2 font-medium">{carDetails.registrationNumber}</span>
                </div>
                <div>
                  <span className="text-gray-600">Token ID:</span>
                  <span className="ml-2 font-medium">{tokenId}</span>
                </div>
              </div>
            </div>

            {/* Owner Information */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Owner Information</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-600">Current Owner:</span>
                  <span className="ml-2 font-mono text-xs">{currentOwner || 'Unknown'}</span>
                </div>
                {web2Details?.contact_info && (
                  <div>
                    <span className="text-gray-600">Contact:</span>
                    <span className="ml-2">{web2Details.contact_info}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Extended Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Description */}
          {web2Details?.description && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <FileText size={20} className="inline mr-2" />
                Description
              </h3>
              <p className="text-sm text-gray-700">{web2Details.description}</p>
            </div>
          )}

          {/* Features */}
          {web2Details?.features && web2Details.features.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <CheckCircle size={20} className="inline mr-2" />
                Features
              </h3>
              <ul className="text-sm text-gray-700 space-y-1">
                {web2Details.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle size={14} className="text-green-500 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Condition Report */}
          {web2Details?.condition_report && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <Shield size={20} className="inline mr-2" />
                Condition Report
              </h3>
              <p className="text-sm text-gray-700">{web2Details.condition_report}</p>
            </div>
          )}

          {/* Service History */}
          {web2Details?.service_history && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <Wrench size={20} className="inline mr-2" />
                Service History
              </h3>
              <p className="text-sm text-gray-700">{web2Details.service_history}</p>
            </div>
          )}

          {/* Seller Notes */}
          {web2Details?.seller_notes && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <FileText size={20} className="inline mr-2" />
                Seller Notes
              </h3>
              <p className="text-sm text-gray-700">{web2Details.seller_notes}</p>
            </div>
          )}
        </div>

        {/* Offers Section */}
        <div className="mt-8">
          <OffersManagement 
            tokenId={tokenId}
            sellerAddress={currentOwner || ''}
            isOwner={isOwner}
          />
        </div>
      </div>

      {/* Purchase Confirmation Modal */}
      {showPurchaseModal && listingDetails && currentOwner && (
        <PurchaseConfirmationModal
          listing={{
            carDetails: {
              ...carDetails,
              createdAt: BigInt(0) // Add missing field with default value
            },
            price: listingDetails.price,
            seller: currentOwner || '',
            tokenId: BigInt(tokenId),
            listedAt: listingDetails.listedAt,
            isActive: listingDetails.isActive
          }}
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          onConfirmPurchase={handlePurchase}
          isPurchasing={isPurchasing}
        />
      )}

      {/* Make Offer Modal */}
      {showOfferModal && currentOwner && (
        <MakeOfferModal
          isOpen={showOfferModal}
          onClose={() => setShowOfferModal(false)}
          tokenId={tokenId}
          sellerAddress={currentOwner || ''}
          currentPrice={price || undefined}
          carInfo={{
            make: carDetails.make,
            model: carDetails.model,
            year: carDetails.year.toString()
          }}
        />
      )}
    </div>
  );
}