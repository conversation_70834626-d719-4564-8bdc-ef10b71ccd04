// src/app/mymech/_components/DiagnosisForm.tsx
import React, { useState } from "react";
import Image from "next/image";
import { Loader, Camera, X, Upload } from "lucide-react";

interface DiagnosisFormProps {
  userQuery: string;
  setUserQuery: (query: string) => void;
  onSubmit: () => void;
  loading: boolean;
  error: string | null;
  images: File[];
  setImages: (images: File[]) => void;
}

export default function DiagnosisForm({
  userQuery,
  setUserQuery,
  onSubmit,
  loading,
  error,
  images,
  setImages,
}: DiagnosisFormProps) {
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [uploadFeedback, setUploadFeedback] = useState<string | null>(null);  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    const validFiles: File[] = [];
    const rejectedFiles: string[] = [];
    
    selectedFiles.forEach(file => {
      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        rejectedFiles.push(`${file.name} (not an image)`);
        return;
      }
      
      // Check file size (1MB = 1024 * 1024 bytes)
      if (file.size > 1024 * 1024) {
        rejectedFiles.push(`${file.name} (too large, max 1MB)`);
        return;
      }
      
      validFiles.push(file);
    });
    
    if (validFiles.length > 0) {
      setImages([...images, ...validFiles]);
      setUploadFeedback(`Added ${validFiles.length} image(s) successfully`);
      setTimeout(() => setUploadFeedback(null), 3000);
    }
    
    if (rejectedFiles.length > 0) {
      setUploadFeedback(`Rejected: ${rejectedFiles.join(', ')}`);
      setTimeout(() => setUploadFeedback(null), 5000);
    }
    
    e.target.value = ''; // Reset input
  };

  // Remove an uploaded image
  const removeImage = (indexToRemove: number) => {
    setImages(images.filter((_, index) => index !== indexToRemove));
  };

  // Create preview URL for image
  const getImagePreviewUrl = (file: File) => {
    return URL.createObjectURL(file);
  };
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Describe Your Vehicle Issue
      </h3>

      <p className="text-gray-600 mb-4">
        Provide details about the symptoms, when they occur, and any relevant
        information that might help diagnose the problem.
      </p>      <div className="mb-4">
        <textarea
          value={userQuery}
          onChange={(e) => setUserQuery(e.target.value)}
          placeholder="E.g., My car is making a grinding noise when I brake, or the check engine light is on and the car feels sluggish when accelerating..."
          className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          rows={5}
        />
      </div>

      {/* Image Upload Section */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-md font-medium text-gray-700">
            Upload Images (Optional)
          </h4>          <button
            type="button"
            onClick={() => setShowImageUpload(!showImageUpload)}
            className="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-600 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
          >
            <Camera size={16} className="mr-2" />
            {showImageUpload ? 'Hide Camera' : 'Add Photos'}
          </button>
        </div>        <p className="text-sm text-gray-500 mb-3">
          Take photos or upload images of the issue, dashboard warnings, or any visual symptoms to help with diagnosis.
        </p>

        {/* Upload feedback */}
        {uploadFeedback && (
          <div className={`p-3 rounded-md mb-3 text-sm ${
            uploadFeedback.includes('Rejected') 
              ? 'bg-red-50 text-red-700 border border-red-200' 
              : 'bg-green-50 text-green-700 border border-green-200'
          }`}>
            {uploadFeedback}
          </div>
        )}{/* Display uploaded images */}
        {images.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-4">
            {images.map((file, index) => (
              <div key={index} className="relative group">
                <Image
                  src={getImagePreviewUrl(file)}
                  alt={`Upload ${index + 1}`}
                  width={120}
                  height={96}
                  className="w-full h-24 object-cover rounded-md border border-gray-200"
                />
                <button
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>
        )}        {/* Image Upload Component */}
        {showImageUpload && (
          <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
            <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center">
              <Upload size={40} className="text-gray-400 mb-4" />
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Take a photo or select images of your vehicle issue
                </p>
                <p className="text-xs text-gray-500">
                  Supported formats: JPG, PNG, GIF (max 1MB each)
                </p>
              </div>
              
              {/* Camera capture input */}
              <input
                type="file"
                id="cameraInput"
                accept="image/*"
                capture="environment"
                onChange={handleFileChange}
                className="hidden"
              />
              
              {/* File upload input */}
              <input
                type="file"
                id="imageInput"
                multiple
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
              
              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                <label
                  htmlFor="cameraInput"
                  className="cursor-pointer py-2 px-4 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <Camera size={16} className="mr-2" />
                  Take Photo
                </label>
                
                <label
                  htmlFor="imageInput"
                  className="cursor-pointer py-2 px-4 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <Upload size={16} className="mr-2" />
                  Choose Files
                </label>
              </div>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="flex justify-end">
        <button
          onClick={onSubmit}
          disabled={loading || !userQuery.trim()}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center"
        >
          {loading ? (
            <>
              <Loader size={18} className="animate-spin mr-2" />
              Analyzing...
            </>
          ) : (
            "Diagnose Issue"
          )}
        </button>
      </div>
    </div>
  );
}
