"use client";

import React, { ReactNode } from "react";
import { ThirdwebProvider } from "thirdweb/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiProvider } from "wagmi";
import { config } from "@/blockchain/config/wagmi";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 30_000,
    },
  },
});

interface ProvidersProps {
  children: ReactNode;
}

export const Providers: React.FC<ProvidersProps> = ({
  children,
}: ProvidersProps) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThirdwebProvider>
        <WagmiProvider config={config}>
          {children}
        </WagmiProvider>
      </ThirdwebProvider>
    </QueryClientProvider>
  );
};
