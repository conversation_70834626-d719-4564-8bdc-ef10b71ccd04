"use client";

import React from "react";
import { useIssueReports, useResolveIssue } from "@/blockchain/hooks/useCarNFTHybrid";
import {
  <PERSON><PERSON><PERSON>riangle,
  FileText,
  CheckCircle,
  Loader,
  XCircle,
  Printer,
  RefreshCw,
} from "lucide-react";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";

interface IssueReport {
  timestamp: bigint;
  issueType: string;
  description: string;
  resolved: boolean;
  evidenceURI: string;
}

interface IssueReportsListProps {
  tokenId: string;
}

const IssueReportsList: React.FC<IssueReportsListProps> = ({ tokenId }) => {
  const {
    data: reports,
    isLoading,
    isError,
    error: fetchError,
    refetch,
  } = useIssueReports(BigInt(tokenId));

  const { resolveIssue, isResolving, clearError } = useResolveIssue();
  
  // Track which issue is currently being resolved
  const [resolvingIndex, setResolvingIndex] = React.useState<number | null>(null);
  // Handle resolving an issue
  const handleResolveIssue = async (originalIndex: number, report: IssueReport) => {
    // Check if the issue is already resolved before attempting to resolve
    if (report.resolved) {
      console.warn("Issue is already resolved");
      alert("This issue has already been resolved.");
      return;
    }

    // Clear any previous errors
    clearError();
    setResolvingIndex(originalIndex);

    try {
      await resolveIssue({
        tokenId: BigInt(tokenId),
        reportIndex: BigInt(originalIndex),
        onSuccess: () => {
          console.log("Issue resolved successfully");
          setResolvingIndex(null);
          setTimeout(() => refetch?.(), 2000);
        },
        onError: (error) => {
          console.error("Error resolving issue:", error);
          setResolvingIndex(null);
          
          // Show user-friendly error message
          if (error.message.includes("Issue already resolved")) {
            alert("This issue has already been resolved.");
          } else if (error.message.includes("You are not authorized")) {
            alert("You are not authorized to resolve this issue.");
          } else if (error.message.includes("Issue report does not exist")) {
            alert("This issue report no longer exists.");
          } else if (error.message.includes("Vehicle not found")) {
            alert("Vehicle not found.");
          } else if (error.message.includes("Vehicle has been deleted")) {
            alert("This vehicle has been deleted.");
          } else {
            alert(`Failed to resolve issue: ${error.message}`);
          }
        },
      });
    } catch (error) {
      console.error("Error resolving issue:", error);
      setResolvingIndex(null);
      
      // Handle any additional errors that might occur
      if (error instanceof Error && error.message.includes("Issue already resolved")) {
        alert("This issue has already been resolved.");
      } else {
        alert("Failed to resolve issue. Please try again.");
      }
    }
  };

  const handlePrintIssues = () => {
    const doc = new jsPDF();
    const title = "Vehicle Issue Reports";

    // Add title
    doc.setFontSize(16);
    doc.text(title, 14, 20);

    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 30);

    const tableData = sortedReports.map((report) => [
      new Date(Number(report.timestamp) * 1000).toLocaleDateString(),
      report.issueType,
      report.description,
      report.resolved ? "Resolved" : "Pending",
      report.evidenceURI ? "Yes" : "No",
    ]);

    autoTable(doc, {
      head: [["Date", "Issue Type", "Description", "Status", "Evidence"]],
      body: tableData,
      startY: 35,
      styles: { fontSize: 9 },
      headStyles: { fillColor: [245, 158, 11] }, // Yellow color for issues
    });

    doc.save(`issue-reports-${tokenId}.pdf`);
  };

  // Display loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader size={24} className="animate-spin text-blue-600 mr-2" />
        <span className="text-blue-600">Loading issue reports...</span>
      </div>
    );
  }

  // Display error state with detailed message
  if (isError || !reports) {
    const errorMessage =
      fetchError instanceof Error ? fetchError.message : String(fetchError);
    return (
      <div className="rounded-md bg-red-50 p-4 mb-4">
        <div className="flex">
          <XCircle size={16} className="h-5 w-5 text-red-400" />
          <div className="ml-3 overflow-hidden">
            <h3 className="text-sm font-medium text-red-800">
              Error loading issue reports
            </h3>
            {errorMessage && (
              <div className="text-xs text-red-700 mt-1 overflow-auto max-h-32">
                <p>{errorMessage}</p>
              </div>
            )}
            <button
              onClick={() => refetch?.()}
              className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded-md text-sm flex items-center"
            >
              <RefreshCw size={14} className="mr-1" />
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Display empty state
  if (!Array.isArray(reports) || reports.length === 0) {
    return (
      <div className="rounded-md bg-gray-50 p-4 mb-4 border border-gray-200">
        <div className="flex justify-center items-center py-4">
          <CheckCircle size={20} className="text-green-500 mr-2" />
          <p className="text-gray-500">No issues reported for this vehicle.</p>
        </div>
      </div>
    );
  }

  // Sort reports by timestamp (newest first) and keep track of original indices
  const sortedReportsWithIndex = [...(reports as IssueReport[])].map((report, originalIndex) => ({
    report,
    originalIndex
  })).sort((a, b) => Number(b.report.timestamp) - Number(a.report.timestamp));

  const sortedReports = sortedReportsWithIndex.map(item => item.report);

  // Helper function to format IPFS URI
  const formatIPFSLink = (uri: string): string | undefined => {
    if (!uri) return undefined;

    if (uri.startsWith("ipfs://")) {
      return `https://ipfs.io/ipfs/${uri.replace("ipfs://", "")}`;
    }
    return uri;
  };

  return (
    <div className="border border-gray-200 rounded-md overflow-hidden">
      <div className="bg-amber-50 p-2 border-b border-gray-200 flex justify-between items-center mb-2">
        <div className="flex items-center">
          <AlertTriangle size={16} className="text-amber-500 mr-2" />
          <span className="text-amber-700 font-medium">
            {sortedReports.length} issue reports
          </span>
        </div>
        <button
          onClick={handlePrintIssues}
          className="text-amber-600 hover:text-amber-800 p-1 rounded flex items-center gap-1"
          title="Print issue reports"
        >
          <Printer size={16} />
          <span className="text-sm">Print</span>
        </button>
      </div>
      <ul className="divide-y divide-gray-200">
        {sortedReportsWithIndex.map((item, index) => {
          const { report, originalIndex } = item;
          return (
            <li
              key={index}
              className={`p-4 hover:bg-gray-50 ${
                report.resolved ? "bg-green-50" : ""
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  {report.resolved ? (
                    <CheckCircle size={16} className="text-green-500 mr-2" />
                  ) : (
                    <AlertTriangle size={16} className="text-amber-500 mr-2" />
                  )}
                  <span className="font-medium">{report.issueType}</span>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(Number(report.timestamp) * 1000).toLocaleDateString()}
                </span>
              </div>
              <div className="ml-6 space-y-2">
                <p className="text-sm text-gray-600">{report.description}</p>

                <div className="flex justify-between items-center mt-2">
                  {report.evidenceURI && (
                    <a
                      href={formatIPFSLink(report.evidenceURI)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <FileText size={14} className="mr-1" />
                      View Evidence
                    </a>
                  )}

                  {!report.resolved ? (
                    <button
                      onClick={() => handleResolveIssue(originalIndex, report)}
                      disabled={isResolving || report.resolved || resolvingIndex === originalIndex}
                      className={`inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded ${
                        isResolving || report.resolved || resolvingIndex === originalIndex
                          ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                          : "text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      }`}
                    >
                      {resolvingIndex === originalIndex ? (
                        <>
                          <Loader size={12} className="animate-spin mr-1" />
                          Resolving...
                        </>
                      ) : (
                        <>
                          <CheckCircle size={12} className="mr-1" />
                          Mark as Resolved
                        </>
                      )}
                    </button>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-green-700 bg-green-100 rounded">
                      <CheckCircle size={12} className="mr-1" />
                      Resolved
                    </span>
                  )}
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default IssueReportsList;
