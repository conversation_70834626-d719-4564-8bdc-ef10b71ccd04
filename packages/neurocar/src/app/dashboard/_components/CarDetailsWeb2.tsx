"use client";

import React, { useState } from "react";
import { useCarDetailsWeb2 } from "@/hooks/useCarDetailsWeb2";
import { Camera, Plus, X, Save, Edit, MapPin, FileText } from "lucide-react";
import IPFSUpload from "./IPFSUpload";
import Image from "next/image";

interface CarDetailsWeb2Props {
  tokenId: string;
}

const CarDetailsWeb2: React.FC<CarDetailsWeb2Props> = ({ tokenId }) => {
  const {
    details,
    images,
    loading,
    error,
    updateDetails,
    addImage,
    removeImage,
    refetch
  } = useCarDetailsWeb2(tokenId);

  const [isEditing, setIsEditing] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [formData, setFormData] = useState({
    description: details?.description || "",
    features: details?.features || [],
    condition_report: details?.condition_report || "",
    service_history: details?.service_history || "",
    accident_history: details?.accident_history || "",
    modifications: details?.modifications || [],
    seller_notes: details?.seller_notes || "",
    location: details?.location || "",
    contact_info: details?.contact_info || ""
  });

  React.useEffect(() => {
    if (details) {
      setFormData({
        description: details.description || "",
        features: details.features || [],
        condition_report: details.condition_report || "",
        service_history: details.service_history || "",
        accident_history: details.accident_history || "",
        modifications: details.modifications || [],
        seller_notes: details.seller_notes || "",
        location: details.location || "",
        contact_info: details.contact_info || ""
      });
    }
  }, [details]);

  const handleSave = async () => {
    const result = await updateDetails(formData);
    if (result.success) {
      setIsEditing(false);
      refetch();
    } else {
      alert(
        "Failed to save details: " +
        (typeof result.error === "object" && result.error !== null && "message" in result.error
          ? (result.error as any).message
          : JSON.stringify(result.error) || "Unknown error")
      );
    }
  };

  const handleImageUpload = async (imageUrl: string, imageType: string) => {
    const result = await addImage({
      token_id: tokenId,
      image_url: imageUrl,
      image_type: imageType as any,
      is_primary: images.length === 0
    });

    if (result.success) {
      setShowImageUpload(false);
      refetch();
    } else {
      alert(
        "Failed to add image: " +
        (typeof result.error === "object" && result.error !== null && "message" in result.error
          ? (result.error as any).message
          : JSON.stringify(result.error) || "Unknown error")
      );
    }
  };

  const handleRemoveImage = async (imageId: string) => {
    if (confirm("Are you sure you want to remove this image?")) {
      const result = await removeImage(imageId);
      if (!result.success) {
        alert(
          "Failed to remove image: " +
          (typeof result.error === "object" && result.error !== null && "message" in result.error
            ? (result.error as any).message
            : JSON.stringify(result.error) || "Unknown error")
        );
      }
    }
  };

  if (loading) {
    return <div className="p-6 text-center">Loading additional details...</div>;
  }

  if (error && error.code === '42P01') {
    return (
      <div className="p-6 text-center">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">
            Database Setup Required
          </h3>
          <p className="text-yellow-700">
            The additional car details feature requires Supabase database setup.
            Please run the database migrations to enable this functionality.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Additional Details</h3>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <Edit size={16} className="mr-2" />
          {isEditing ? "Cancel" : "Edit"}
        </button>
      </div>

      {/* Image Gallery */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex justify-between items-center mb-4">
          <h4 className="font-medium text-gray-900">Image Gallery</h4>
          <button
            onClick={() => setShowImageUpload(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Plus size={16} className="mr-2" />
            Add Image
          </button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={image.image_url}
                  alt={image.description || "Car image"}
                  className="w-full h-full object-cover"
                  width={200}
                  height={200}
                />
              </div>
              <button
                onClick={() => handleRemoveImage(image.id!)}
                className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X size={14} />
              </button>
              <div className="mt-1 text-xs text-gray-600 capitalize">
                {image.image_type}
                {image.is_primary && " (Primary)"}
              </div>
            </div>
          ))}
        </div>

        {images.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Camera size={48} className="mx-auto mb-2 text-gray-300" />
            <p>No additional images uploaded</p>
          </div>
        )}
      </div>

      {/* Details Form */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            {isEditing ? (
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Detailed description of the vehicle..."
              />
            ) : (
              <p className="text-gray-900">{formData.description || "No description provided"}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <MapPin size={16} className="inline mr-1" />
                Location
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="City, State"
                />
              ) : (
                <p className="text-gray-900">{formData.location || "Not specified"}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Information
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.contact_info}
                  onChange={(e) => setFormData({ ...formData, contact_info: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Phone or email"
                />
              ) : (
                <p className="text-gray-900">{formData.contact_info || "Not provided"}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              <FileText size={16} className="inline mr-1" />
              Condition Report
            </label>
            {isEditing ? (
              <textarea
                value={formData.condition_report}
                onChange={(e) => setFormData({ ...formData, condition_report: e.target.value })}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Current condition of the vehicle..."
              />
            ) : (
              <p className="text-gray-900">{formData.condition_report || "No condition report provided"}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Seller Notes
            </label>
            {isEditing ? (
              <textarea
                value={formData.seller_notes}
                onChange={(e) => setFormData({ ...formData, seller_notes: e.target.value })}
                rows={2}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes from the seller..."
              />
            ) : (
              <p className="text-gray-900">{formData.seller_notes || "No additional notes"}</p>
            )}
          </div>
        </div>

        {isEditing && (
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleSave}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        )}
      </div>

      {/* Image Upload Modal */}
      {showImageUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Add New Image</h3>
              <button
                onClick={() => setShowImageUpload(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image Type
              </label>
              <select className="w-full p-2 border border-gray-300 rounded-md">
                <option value="exterior">Exterior</option>
                <option value="interior">Interior</option>
                <option value="engine">Engine</option>
                <option value="documents">Documents</option>
                <option value="other">Other</option>
              </select>
            </div>

            <IPFSUpload
              onUploadComplete={(url) => handleImageUpload(url, "exterior")}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CarDetailsWeb2;
