export interface CarDetailsWeb2 {
  id?: string;
  token_id: string;
  additional_images: string[];
  description: string;
  features: string[];
  condition_report: string;
  service_history: string;
  accident_history: string;
  modifications: string[];
  seller_notes: string;
  location: string;
  contact_info: string;
  created_at?: string;
  updated_at?: string;
}

export interface MarketplaceListing {
  id?: string;
  token_id: string;
  seller_address: string;
  price_usdt: string;
  listing_description: string;
  is_active: boolean;
  featured: boolean;
  views_count: number;
  created_at?: string;
  updated_at?: string;
}

export interface CarImage {
  id?: string;
  token_id: string;
  image_url: string;
  image_type: 'exterior' | 'interior' | 'engine' | 'documents' | 'other';
  description?: string;
  is_primary: boolean;
  created_at?: string;
}

export interface Offer {
  id?: string;
  token_id: string;
  buyer_address: string;
  seller_address: string;
  offer_amount: string;
  message: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn' | 'completed';
  expires_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Database {
  public: {
    Tables: {
      car_details_web2: {
        Row: CarDetailsWeb2;
        Insert: Omit<CarDetailsWeb2, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<CarDetailsWeb2, 'id' | 'created_at' | 'updated_at'>>;
      };
      marketplace_listings: {
        Row: MarketplaceListing;
        Insert: Omit<MarketplaceListing, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<MarketplaceListing, 'id' | 'created_at' | 'updated_at'>>;
      };
      car_images: {
        Row: CarImage;
        Insert: Omit<CarImage, 'id' | 'created_at'>;
        Update: Partial<Omit<CarImage, 'id' | 'created_at'>>;
      };
      offers: {
        Row: Offer;
        Insert: Omit<Offer, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Offer, 'id' | 'created_at' | 'updated_at'>>;
      };
    };
  };
}
