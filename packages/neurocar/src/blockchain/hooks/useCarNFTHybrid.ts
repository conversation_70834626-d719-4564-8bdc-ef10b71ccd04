"use client";

import { useState } from 'react';
import { type Address, type Hash, type Abi } from 'viem';
import { carnft_abi, carnft_address } from '@/blockchain/abi/neuro';
import { useContractRead } from './useContractReads';
import { MaintenanceRecord, InsuranceDetail, IssueReport } from '@/types';
import { 
  useThirdwebWagmiAccount, 
  useThirdwebWriteContract, 
  useThirdwebWaitForTransaction 
} from './useThirdwebWagmiAdapter';

// --- Base interface for transaction props ---
interface BaseTxnProps {
  onSuccess?: (hash: Hash) => void;
  onError?: (error: Error) => void;
}

// --- Hook for Adding Maintenance Record ---
interface UseAddMaintenanceRecordProps {
  tokenId: bigint;
  description: string;
  serviceProvider: string;
  mileage: bigint;
  documentURI: string;
  onSuccess?: (hash: Hash) => void;
  onError?: (error: Error) => void;
}

export function useAddMaintenanceRecord() {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const { writeContract, isLoading: isWriting, hash, error: writeError } = useThirdwebWriteContract();
  const { isLoading: isConfirming, isSuccess } = useThirdwebWaitForTransaction(hash);
  const [error, setError] = useState<Error | null>(null);

  const addRecord = async ({ 
    tokenId, 
    description, 
    serviceProvider, 
    mileage, 
    documentURI, 
    onSuccess, 
    onError 
  }: UseAddMaintenanceRecordProps) => {
    if (!connectedAddress) {
        const err = new Error('Wallet not connected');
        setError(err);
        if (onError) onError(err);
        return;
    }
    
    setError(null);

    try {
      await writeContract({
        address: carnft_address as Address,
        abi: carnft_abi as Abi,
        functionName: 'addMaintenanceRecord',
        args: [tokenId, description, serviceProvider, mileage, documentURI],
        onSuccess: (txHash) => {
          console.log('Add maintenance record transaction sent:', txHash);
          if (onSuccess) onSuccess(txHash);
        },
        onError: (err) => {
          console.error('Add maintenance record error:', err);
          setError(err);
          if (onError) onError(err);
        },
      });
    } catch (err) {
      console.error('Failed to send add maintenance record transaction:', err);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      if (onError) onError(error);
    }
  };
  
  const clearError = () => setError(null);

  return { 
    addRecord, 
    isAdding: isWriting || isConfirming, 
    isConfirmed: isSuccess,
    hash, 
    error: error || writeError,
    clearError 
  };
}

// --- Hook for Minting a New Car ---
interface UseMintCarProps {
  to: Address;
  vin: string;
  make: string;
  model: string;
  year: bigint;
  registrationNumber: string;
  imageURI: string;
  onSuccess?: (hash: Hash) => void;
  onError?: (error: Error) => void;
}

export function useMintCar() {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const { writeContract, isLoading: isWriting, hash, error: writeError } = useThirdwebWriteContract();
  const { isLoading: isConfirming, isSuccess } = useThirdwebWaitForTransaction(hash);
  const [error, setError] = useState<Error | null>(null);

  const mintCar = async ({ 
    to, 
    vin, 
    make, 
    model, 
    year, 
    registrationNumber, 
    imageURI, 
    onSuccess, 
    onError 
  }: UseMintCarProps) => {
    if (!connectedAddress) {
        const err = new Error('Wallet not connected');
        setError(err);
        if (onError) onError(err);
        return;
    }
    
    setError(null);
    
    try {
      await writeContract({
        address: carnft_address as Address,
        abi: carnft_abi as Abi,
        functionName: 'mintCar',
        args: [to, vin, make, model, year, registrationNumber, imageURI],
        onSuccess: (txHash) => {
          console.log('Mint car transaction sent:', txHash);
          if (onSuccess) onSuccess(txHash);
        },
        onError: (err) => {
          console.error('Mint car error:', err);
          setError(err);
          if (onError) onError(err);
        },
      });
    } catch (err) {
      console.error('Failed to send mint car transaction:', err);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      if (onError) onError(error);
    }
  };

  const clearError = () => setError(null);

  return { 
    mintCar, 
    isMinting: isWriting || isConfirming, 
    isConfirmed: isSuccess,
    hash, 
    error: error || writeError,
    clearError 
  };
}

// --- Hook for Reporting Issues ---
interface UseReportIssueProps {
  tokenId: bigint;
  issueType: string;
  description: string;
  evidenceURI: string;
  onSuccess?: (hash: Hash) => void;
  onError?: (error: Error) => void;
}

export function useReportIssue() {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const { writeContract, isLoading: isWriting, hash, error: writeError } = useThirdwebWriteContract();
  const { isLoading: isConfirming, isSuccess } = useThirdwebWaitForTransaction(hash);
  const [error, setError] = useState<Error | null>(null);

  const reportIssue = async ({ 
    tokenId, 
    issueType, 
    description, 
    evidenceURI, 
    onSuccess, 
    onError 
  }: UseReportIssueProps) => {
    if (!connectedAddress) {
        const err = new Error('Wallet not connected');
        setError(err);
        if (onError) onError(err);
        return;
    }
    
    setError(null);

    try {
      await writeContract({
        address: carnft_address as Address,
        abi: carnft_abi as Abi,
        functionName: 'reportIssue',
        args: [tokenId, issueType, description, evidenceURI],
        onSuccess: (txHash) => {
          console.log('Report issue transaction sent:', txHash);
          if (onSuccess) onSuccess(txHash);
        },
        onError: (err) => {
          console.error('Report issue error:', err);
          setError(err);
          if (onError) onError(err);
        },
      });
    } catch (err) {
      console.error('Failed to send report issue transaction:', err);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      if (onError) onError(error);
    }
  };

  const clearError = () => setError(null);

  return { 
    reportIssue, 
    isReporting: isWriting || isConfirming, 
    isConfirmed: isSuccess,
    hash, 
    error: error || writeError,
    clearError 
  };
}

// --- Hook for Resolving Issues (Hybrid: Thirdweb writes) ---
interface UseResolveIssueProps extends BaseTxnProps {
  tokenId: bigint;
  reportIndex: bigint;
}

export function useResolveIssue() {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const { writeContract, isLoading: isWriting, hash, error: writeError } = useThirdwebWriteContract();
  const { isLoading: isConfirming, isSuccess } = useThirdwebWaitForTransaction(hash);
  const [error, setError] = useState<Error | null>(null);

  const resolveIssue = async ({ 
    tokenId, 
    reportIndex, 
    onSuccess, 
    onError 
  }: UseResolveIssueProps) => {
    if (!connectedAddress) {
        const err = new Error('Wallet not connected');
        setError(err);
        if (onError) onError(err);
        return;
    }
    
    setError(null);

    try {
      await writeContract({
        address: carnft_address as Address,
        abi: carnft_abi as Abi,
        functionName: 'resolveIssue',
        args: [tokenId, reportIndex],
        onSuccess: (txHash) => {
          console.log('Resolve issue transaction sent:', txHash);
          if (onSuccess) onSuccess(txHash);
        },
        onError: (err) => {
          console.error('Resolve issue error:', err);
          
          // Enhanced error handling for specific contract errors
          let enhancedError = err;
          if (err && typeof err === 'object' && 'message' in err) {
            const errorMessage = String(err.message);
            if (errorMessage.includes('Issue already resolved')) {
              enhancedError = new Error('Issue already resolved');
            } else if (errorMessage.includes('Not owner nor approved')) {
              enhancedError = new Error('You are not authorized to resolve this issue');
            } else if (errorMessage.includes('Report does not exist')) {
              enhancedError = new Error('Issue report does not exist');
            } else if (errorMessage.includes('Car does not exist')) {
              enhancedError = new Error('Vehicle not found');
            } else if (errorMessage.includes('Car has been deleted')) {
              enhancedError = new Error('Vehicle has been deleted');
            }
          }
          
          setError(enhancedError as Error);
          if (onError) onError(enhancedError as Error);
        },
      });
    } catch (err) {
      console.error('Failed to send resolve issue transaction:', err);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      if (onError) onError(error);
    }
  };

  const clearError = () => setError(null);

  return { 
    resolveIssue, 
    isResolving: isWriting || isConfirming, 
    isConfirmed: isSuccess,
    hash, 
    error: error || writeError,
    clearError 
  };
}

// --- Hook for Processing Insurance (Hybrid: Thirdweb writes) ---
interface UseProcessInsuranceProps extends BaseTxnProps {
  tokenId: bigint;
  policyNumber: string;
  provider: string;
  startDate: bigint;
  endDate: bigint;
  documentURI: string;
  isUpdate: boolean;
  active: boolean;
}

export function useProcessInsurance() {
  const { address: connectedAddress } = useThirdwebWagmiAccount();
  const { writeContract, isLoading: isWriting, hash, error: writeError } = useThirdwebWriteContract();
  const { isLoading: isConfirming, isSuccess } = useThirdwebWaitForTransaction(hash);
  const [error, setError] = useState<Error | null>(null);
  const processInsurance = async ({ 
    tokenId, 
    policyNumber, 
    provider, 
    startDate, 
    endDate, 
    documentURI, 
    isUpdate, 
    active, 
    onSuccess, 
    onError 
  }: UseProcessInsuranceProps) => {
    console.log('ProcessInsurance called with:', {
      tokenId: tokenId.toString(),
      policyNumber,
      provider,
      startDate: startDate.toString(),
      endDate: endDate.toString(),
      documentURI,
      isUpdate,
      active,
      connectedAddress
    });

    if (!connectedAddress) {
        const err = new Error('Wallet not connected');
        console.error('Wallet not connected');
        setError(err);
        if (onError) onError(err);
        return;
    }
    
    setError(null);

    try {
      const functionName = isUpdate ? 'updateInsuranceDetails' : 'addInsuranceDetails';
      const args = isUpdate 
        ? [tokenId, policyNumber, provider, startDate, endDate, active, documentURI]
        : [tokenId, policyNumber, provider, startDate, endDate, documentURI];

      console.log('Calling contract function:', functionName, 'with args:', args);

      await writeContract({
        address: carnft_address as Address,
        abi: carnft_abi as Abi,
        functionName,
        args,
        onSuccess: (txHash) => {
          console.log('Process insurance transaction sent:', txHash);
          if (onSuccess) onSuccess(txHash);
        },
        onError: (err) => {
          console.error('Process insurance error:', err);
          setError(err);
          if (onError) onError(err);
        },
      });
    } catch (err) {
      console.error('Failed to send process insurance transaction:', err);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      if (onError) onError(error);
    }
  };

  const clearError = () => setError(null);

  return { 
    processInsurance, 
    isProcessing: isWriting || isConfirming, 
    isConfirmed: isSuccess,
    hash, 
    error: error || writeError,
    clearError 
  };
}

// --- Read Hooks (Keep using Wagmi for reads) ---

// Get Car Details
export function useCarDetails(tokenId?: bigint) {
  return useContractRead<{
    vin: string;
    make: string;
    model: string;
    year: bigint;
    registrationNumber: string;
    imageURI: string;
    currentOwner: Address;
    isActive: boolean;
  }>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getCarDetails',
    args: tokenId ? [tokenId] : undefined,
    enabled: !!tokenId,
  });
}

// Get Maintenance Records
export function useMaintenanceRecords(tokenId?: bigint) {
  return useContractRead<MaintenanceRecord[]>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getMaintenanceHistory',
    args: tokenId ? [tokenId] : undefined,
    enabled: !!tokenId,
  });
}

// Get Insurance Details
export function useInsuranceDetails2(tokenId?: bigint) {
  return useContractRead<InsuranceDetail>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getInsuranceDetails',
    args: tokenId ? [tokenId] : undefined,
    enabled: !!tokenId,
  });
}

// Get Issue Reports
export function useIssueReports(tokenId?: bigint) {
  return useContractRead<IssueReport[]>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getIssueReports',
    args: tokenId ? [tokenId] : undefined,
    enabled: !!tokenId,
  });
}

// Get TokenId by VIN
export function useTokenIdByVIN(vin?: string) {
  return useContractRead<bigint>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getTokenIdByVIN',
    args: vin ? [vin] : undefined,
    enabled: !!vin,
  });
}

// Get TokenId by Registration Number
export function useTokenIdByRegistration(registrationNumber?: string) {
  return useContractRead<bigint>({
    address: carnft_address as Address,
    abi: carnft_abi as Abi,
    functionName: 'getTokenIdByRegistrationNumber',
    args: registrationNumber ? [registrationNumber] : undefined,
    enabled: !!registrationNumber,
  });
}

// Combined hook for CarNFT operations
export function useCarNFTData() {
  const { mintCar, isMinting, error: mintError } = useMintCar();
  const { addRecord: addMaintenanceRecord, isAdding: isAddingMaintenance, error: maintenanceError } = useAddMaintenanceRecord();
  const { reportIssue, isReporting, error: reportError } = useReportIssue();
  const { resolveIssue, isResolving, error: resolveError } = useResolveIssue();
  const { processInsurance, isProcessing, error: insuranceError } = useProcessInsurance();

  const handleMintCar = async (
    address: Address,
    vin: string,
    make: string,
    model: string,
    year: number,
    registrationNumber: string,
    imageURI: string
  ) => {
    return await mintCar({
      to: address,
      vin,
      make,
      model,
      year: BigInt(year),
      registrationNumber,
      imageURI
    });
  };

  const handleAddMaintenanceRecord = async (
    tokenId: bigint,
    description: string,
    serviceProvider: string,
    mileage: number,
    documentURI: string
  ) => {
    return await addMaintenanceRecord({
      tokenId,
      description,
      serviceProvider,
      mileage: BigInt(mileage),
      documentURI
    });
  };

  const handleReportIssue = async (
    tokenId: bigint,
    issueType: string,
    description: string,
    evidenceURI: string
  ) => {
    return await reportIssue({
      tokenId,
      issueType,
      description,
      evidenceURI
    });
  };

  const handleResolveIssue = async (
    tokenId: bigint,
    reportIndex: bigint
  ) => {
    return await resolveIssue({
      tokenId,
      reportIndex
    });
  };

  const handleProcessInsurance = async (
    tokenId: bigint,
    policyNumber: string,
    provider: string,
    startDate: number,
    endDate: number,
    documentURI: string,
    isUpdate: boolean,
    active: boolean
  ) => {
    return await processInsurance({
      tokenId,
      policyNumber,
      provider,
      startDate: BigInt(startDate),
      endDate: BigInt(endDate),
      documentURI,
      isUpdate,
      active
    });
  };

  return {
    mintCar: handleMintCar,
    addMaintenanceRecord: handleAddMaintenanceRecord,
    reportIssue: handleReportIssue,
    resolveIssue: handleResolveIssue,
    processInsurance: handleProcessInsurance,
    isMinting,
    isAddingMaintenance,
    isReporting,
    isResolving,
    isProcessing,
    mintError,
    maintenanceError,
    reportError,
    resolveError,
    insuranceError
  };
}
