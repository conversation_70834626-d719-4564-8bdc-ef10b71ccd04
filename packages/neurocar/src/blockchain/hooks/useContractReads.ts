"use client"

import { useReadContract } from 'wagmi';
import { type Address, type Abi } from 'viem';
import { useAccount } from 'wagmi';
import { useMemo } from 'react';
import { carinsurance_abi, carinsurance_address } from '@/blockchain/abi/neuro';

interface UseReadContractProps<T, TArgs = unknown, TData = unknown> {
  address: Address;
  abi: Abi;
  functionName: string;
  args?: TArgs[];
  enabled?: boolean;
  watch?: boolean;
  select?: (data: TData) => T;
}

export function useContractRead<T, TArgs = unknown, TData = unknown>({
  address,
  abi,
  functionName,
  args = [],
  enabled = true,
  watch = false,
  select,
}: UseReadContractProps<T, TArgs, TData>) {
  return useReadContract({
    address,
    abi,
    functionName,
    args,
    query: {
      enabled,
      select: select as ((data: unknown) => T) | undefined,
      ...(watch ? { refetchInterval: 5000 } : {}),
    },
  });
}

// Hook to get claims for a specific car by tokenId
export function useCarClaims(tokenId: bigint, poolId?: number) {
  const { address: connectedAddress } = useAccount();

  // First, get all memberships for the user
  const { data: membershipIds, isLoading: membershipLoading } = useContractRead<bigint[]>({
    address: carinsurance_address as Address,
    abi: carinsurance_abi as Abi,
    functionName: 'getMembershipsByOwner',
    args: connectedAddress ? [connectedAddress] : undefined,
    enabled: !!connectedAddress,
  });
  // Find the membership that matches the tokenId
  const relevantMembershipId = useMemo(() => {
    if (!membershipIds || membershipIds.length === 0) return null;
    
    // For now, we'll use the first membership as a placeholder
    // In a complete implementation, you would need to check each membership's tokenId
    // by calling the memberships mapping for each membershipId
    // TODO: Implement proper tokenId matching logic
    console.log('Finding membership for tokenId:', tokenId, 'poolId:', poolId);
    return membershipIds[0];
  }, [membershipIds, tokenId, poolId]);

  // Get claims for the relevant membership
  const { data: claimIds, isLoading: claimsLoading, error } = useContractRead<bigint[]>({
    address: carinsurance_address as Address,
    abi: carinsurance_abi as Abi,
    functionName: 'getClaimsByMembership',
    args: relevantMembershipId ? [relevantMembershipId] : undefined,
    enabled: !!relevantMembershipId,
  });

  return {
    data: claimIds,
    isLoading: membershipLoading || claimsLoading,
    error,
  };
}