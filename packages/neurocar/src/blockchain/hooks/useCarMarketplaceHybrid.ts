import { useState, useEffect, useMemo } from 'react';
import { type Address, type Hash, type Abi } from 'viem';
import { useReadContract } from 'wagmi';
import { carmarketplace_abi, carmarketplace_address, carnft_abi, carnft_address, usdt_abi, usdt_address } from '@/blockchain/abi/neuro';
import { useContractRead as useAppContractRead } from './useContractReads';
import { CarDetails as CarNFTDetailsType } from '@/types';
import { useThirdwebWriteContract, useThirdwebWaitForTransaction, useThirdwebWagmiAccount } from './useThirdwebWagmiAdapter';

// --- Types ---
interface BaseTxnProps {
    onSuccess?: (hash: Hash) => void;
    onError?: (error: Error) => void;
}

interface UseListCarProps extends BaseTxnProps {
    tokenId: bigint;
    price: bigint;
    description: string;
}

interface UsePurchaseCarProps extends BaseTxnProps {
    tokenId: bigint;
}

interface UseCancelListingProps extends BaseTxnProps {
    tokenId: bigint;
}

// Updated Listing interface to include detailed car info
export interface Listing {
  tokenId: bigint;
  price: bigint;
  seller: Address;
  carDetails: CarNFTDetailsType | null;
  listedAt: bigint;
  isActive: boolean;
}

// --- Hook for Listing a Car (Hybrid: Thirdweb writes + Wagmi reads) ---
export function useListCarHybrid() {
    const { address: connectedAddress } = useThirdwebWagmiAccount();
    const [isListing, setIsListing] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const { writeContract, hash } = useThirdwebWriteContract();
    const { isLoading: isConfirming, isSuccess: isConfirmed } = useThirdwebWaitForTransaction(hash);
    const [listProps, setListProps] = useState<UseListCarProps | null>(null);

    // Read ownerOf from CarNFT using Wagmi
    const { data: ownerOfTokenData, refetch: refetchOwnerOf, isLoading: isLoadingOwner } = useReadContract({
        address: carnft_address as Address,
        abi: carnft_abi,
        functionName: 'ownerOf',
        args: listProps?.tokenId ? [listProps.tokenId] : undefined,
        query: { enabled: !!listProps?.tokenId && !!connectedAddress }
    });
    const ownerOfToken = ownerOfTokenData as Address | undefined;

    // Read getApproved from CarNFT using Wagmi
    const { data: approvedAddressData, refetch: refetchApproved, isLoading: isLoadingApproved } = useReadContract({
        address: carnft_address as Address,
        abi: carnft_abi,
        functionName: 'getApproved',
        args: listProps?.tokenId ? [listProps.tokenId] : undefined,
        query: { enabled: !!listProps?.tokenId && !!connectedAddress }
    });
    const approvedAddress = approvedAddressData as Address | undefined;    const listCar = async (props: UseListCarProps) => {
        if (!connectedAddress) {
            const err = new Error('Wallet not connected');
            setError(err);
            if (props.onError) props.onError(err);
            return;
        }
        setIsListing(true);
        setError(null);
        setListProps(props);

        try {
            await Promise.all([refetchOwnerOf(), refetchApproved()]);
        } catch (readError) {
            console.error('Failed to trigger NFT status refetch:', readError);
        }
    };    // Effect to perform checks and list after data is fetched
    useEffect(() => {
        if (!isListing || !listProps || isLoadingOwner || isLoadingApproved || ownerOfToken === undefined || approvedAddress === undefined) {
            return;
        }

        // Check 1: Ownership
        if (ownerOfToken.toLowerCase() !== connectedAddress?.toLowerCase()) {
            const err = new Error(`You do not own NFT with ID ${listProps.tokenId}. Owner: ${ownerOfToken}`);
            setError(err);
            setIsListing(false);
            if (listProps.onError) listProps.onError(err);
            setListProps(null);
            return;
        }

        // Check 2: Marketplace Approval
        if (approvedAddress.toLowerCase() !== carmarketplace_address.toLowerCase()) {
            const err = new Error(`Marketplace (${carmarketplace_address}) not approved for NFT ID ${listProps.tokenId}. Approved: ${approvedAddress}. Please approve the marketplace first.`);
            setError(err);
            setIsListing(false);
            if (listProps.onError) listProps.onError(err);
            setListProps(null);
            return;
        }

        // If checks pass, proceed with Thirdweb writeContract
        try {
            writeContract({
                address: carmarketplace_address as Address,
                abi: carmarketplace_abi,
                functionName: 'listCar',
                args: [listProps.tokenId, listProps.price, listProps.description],
                onSuccess: (txHash) => {
                    console.log('List car transaction sent:', txHash);
                    if (listProps.onSuccess) listProps.onSuccess(txHash);
                },
                onError: (err) => {
                    console.error('List car write error:', err);
                    setError(err);
                    setIsListing(false);
                    if (listProps.onError) listProps.onError(err);
                    setListProps(null);
                },
            });
        } catch (err) {
            console.error('Write contract error:', err);
            setError(err as Error);
            setIsListing(false);
            if (listProps.onError) listProps.onError(err as Error);
            setListProps(null);
        }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isListing, listProps, ownerOfToken, approvedAddress, isLoadingOwner, isLoadingApproved, connectedAddress]);

    // Reset isListing state when transaction is confirmed or if there was an error
    useEffect(() => {
        if (isConfirmed || (error && !hash)) {
            setIsListing(false);
            setListProps(null);
        }
    }, [isConfirmed, error, hash]);

    const clearError = () => setError(null);

    return {
        listCar,
        isListing: isListing || isConfirming,
        isConfirming,
        isConfirmed,
        hash,
        error,
        clearError
    };
}

// --- Hook for Purchasing a Car (Hybrid: Thirdweb writes + Wagmi reads) ---
export function usePurchaseCarHybrid() {
    const { address: connectedAddress } = useThirdwebWagmiAccount();
    const [isPurchasing, setIsPurchasing] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const { writeContract, hash } = useThirdwebWriteContract();
    const { isLoading: isConfirming, isSuccess: isConfirmed } = useThirdwebWaitForTransaction(hash);
    const [purchaseProps, setPurchaseProps] = useState<UsePurchaseCarProps | null>(null);

    // Read listing details to get the price using Wagmi
    const { data: listingDetailsData, refetch: refetchListingDetails, isLoading: isLoadingListing } = useAppContractRead<[bigint, boolean, bigint, string]>({
        address: carmarketplace_address as Address,
        abi: carmarketplace_abi as Abi,
        functionName: 'getListingDetails',
        args: purchaseProps?.tokenId ? [purchaseProps.tokenId] : undefined,
        enabled: !!purchaseProps?.tokenId,
    });
    const listingDetails = useMemo(() => {
        return listingDetailsData ? {
            price: listingDetailsData[0],
            isActive: listingDetailsData[1],
            listedAt: listingDetailsData[2],
            description: listingDetailsData[3],
        } : undefined;
    }, [listingDetailsData]);
    const price = listingDetails?.price;    // Read USDT balance using Wagmi
    const { data: usdtBalanceData, isLoading: isLoadingBalance } = useReadContract({
        address: usdt_address as Address,
        abi: usdt_abi,
        functionName: 'balanceOf',
        args: connectedAddress ? [connectedAddress] : undefined,
        query: { enabled: !!connectedAddress && price !== undefined && !!purchaseProps }
    });
    const usdtBalance = typeof usdtBalanceData === 'bigint' ? usdtBalanceData : undefined;

    // Read USDT allowance for the marketplace using Wagmi
    const { data: usdtAllowanceData, isLoading: isLoadingAllowance } = useReadContract({
        address: usdt_address as Address,
        abi: usdt_abi,
        functionName: 'allowance',
        args: connectedAddress ? [connectedAddress, carmarketplace_address as Address] : undefined,
        query: { enabled: !!connectedAddress && price !== undefined && !!purchaseProps }
    });
    const usdtAllowance = typeof usdtAllowanceData === 'bigint' ? usdtAllowanceData : undefined;

    const purchaseCar = async (props: UsePurchaseCarProps) => {
        if (!connectedAddress) {
            const err = new Error('Wallet not connected');
            setError(err);
            if (props.onError) props.onError(err);
            return;
        }
        setIsPurchasing(true);
        setError(null);
        setPurchaseProps(props);

        try {
            await refetchListingDetails();
        } catch (readError) {
            console.error('Failed to trigger purchase pre-requisites refetch:', readError);
        }
    };

    // Effect to perform checks and purchase after data is fetched
    useEffect(() => {
        if (!isPurchasing || !purchaseProps || isLoadingListing || isLoadingBalance || isLoadingAllowance || price === undefined || usdtBalance === undefined || usdtAllowance === undefined) {
            return;
        }

        // Check 0: Is listing active?
        if (!listingDetails?.isActive) {
            const err = new Error(`Listing for NFT ID ${purchaseProps.tokenId} is not active.`);
            setError(err);
            setIsPurchasing(false);
            if (purchaseProps.onError) purchaseProps.onError(err);
            setPurchaseProps(null);
            return;
        }

        // Check 1: Sufficient USDT Balance
        if (usdtBalance < price) {
            const err = new Error(`Insufficient USDT balance. Price: ${price.toString()}, Balance: ${usdtBalance.toString()}`);
            setError(err);
            setIsPurchasing(false);
            if (purchaseProps.onError) purchaseProps.onError(err);
            setPurchaseProps(null);
            return;
        }

        // Check 2: Sufficient USDT Allowance for Marketplace
        if (usdtAllowance < price) {
            const err = new Error(`Insufficient USDT allowance for marketplace. Required: ${price.toString()}, Allowance: ${usdtAllowance.toString()}. Please approve USDT spending.`);
            setError(err);
            setIsPurchasing(false);
            if (purchaseProps.onError) purchaseProps.onError(err);
            setPurchaseProps(null);
            return;
        }        // If checks pass, proceed with Thirdweb writeContract
        try {
            writeContract({
                address: carmarketplace_address as Address,
                abi: carmarketplace_abi,
                functionName: 'purchaseCar',
                args: [purchaseProps.tokenId],
                onSuccess: (txHash) => {
                    console.log('Purchase car transaction sent:', txHash);
                    if (purchaseProps.onSuccess) purchaseProps.onSuccess(txHash);
                },
                onError: (err) => {
                    console.error('Purchase car write error:', err);
                    setError(err);
                    setIsPurchasing(false);
                    if (purchaseProps.onError) purchaseProps.onError(err);
                    setPurchaseProps(null);
                },
            });
        } catch (err) {
            console.error('Write contract error:', err);
            setError(err as Error);
            setIsPurchasing(false);
            if (purchaseProps.onError) purchaseProps.onError(err as Error);
            setPurchaseProps(null);
        }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isPurchasing, purchaseProps, price, listingDetails, usdtBalance, usdtAllowance, isLoadingListing, isLoadingBalance, isLoadingAllowance, connectedAddress]);

    // Reset isPurchasing state when transaction is confirmed or fails
    useEffect(() => {
        if (isConfirmed || (error && !hash)) {
            setIsPurchasing(false);
            setPurchaseProps(null);
        }
    }, [isConfirmed, error, hash]);

    const clearError = () => setError(null);

    return {
        purchaseCar,
        isPurchasing: isPurchasing || isConfirming,
        isConfirming,
        isConfirmed,
        hash,
        error,
        clearError
    };
}

// --- Hook for Cancelling a Listing (Hybrid: Thirdweb writes + Wagmi reads) ---
export function useCancelListingHybrid() {
    const { address: connectedAddress } = useThirdwebWagmiAccount();
    const [isCancelling, setIsCancelling] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const { writeContract, hash } = useThirdwebWriteContract();
    const { isLoading: isConfirming, isSuccess: isConfirmed } = useThirdwebWaitForTransaction(hash);
    const [cancelProps, setCancelProps] = useState<UseCancelListingProps | null>(null);

    // Read listing details to check seller using Wagmi
    const { data: listingDetailsData, refetch: refetchListingDetails, isLoading: isLoadingListing } = useAppContractRead<[bigint, boolean, bigint, string]>({
        address: carmarketplace_address as Address,
        abi: carmarketplace_abi as Abi,
        functionName: 'getListingDetails',
        args: cancelProps?.tokenId ? [cancelProps.tokenId] : undefined,
        enabled: !!cancelProps?.tokenId,
    });
    const listingDetails = useMemo(() => listingDetailsData ? {
        price: listingDetailsData[0],
        isActive: listingDetailsData[1],
        listedAt: listingDetailsData[2],
        description: listingDetailsData[3],
    } : undefined, [listingDetailsData]);

    // Read ownerOf from CarNFT using Wagmi
    const { data: ownerOfTokenData, refetch: refetchOwnerOf, isLoading: isLoadingOwner } = useReadContract({
        address: carnft_address as Address,
        abi: carnft_abi,
        functionName: 'ownerOf',
        args: cancelProps?.tokenId ? [cancelProps.tokenId] : undefined,
        query: { enabled: !!cancelProps?.tokenId && !!connectedAddress }
    });
    const ownerOfToken = ownerOfTokenData as Address | undefined;    const cancelListing = async (props: UseCancelListingProps) => {
        if (!connectedAddress) {
            const err = new Error('Wallet not connected');
            setError(err);
            if (props.onError) props.onError(err);
            return;
        }
        setIsCancelling(true);
        setError(null);
        setCancelProps(props);

        try {
            await Promise.all([refetchListingDetails(), refetchOwnerOf()]);
        } catch (readError) {
            console.error('Failed to trigger cancel pre-requisites refetch:', readError);
        }
    };

    // Effect to perform checks and cancel after data is fetched
    useEffect(() => {
        if (!isCancelling || !cancelProps || isLoadingListing || isLoadingOwner || listingDetails === undefined || ownerOfToken === undefined) {
            return;
        }

        // Check 1: Is listing active?
        if (!listingDetails?.isActive) {
            const err = new Error(`Listing for NFT ID ${cancelProps.tokenId} is not active or does not exist.`);
            setError(err);
            setIsCancelling(false);
            if (cancelProps.onError) cancelProps.onError(err);
            setCancelProps(null);
            return;
        }

        // Check 2: Is the connected user the owner/seller?
        if (ownerOfToken.toLowerCase() !== connectedAddress?.toLowerCase()) {
            const err = new Error(`You are not the current owner of NFT ID ${cancelProps.tokenId}. Cannot cancel listing.`);
            setError(err);
            setIsCancelling(false);
            if (cancelProps.onError) cancelProps.onError(err);
            setCancelProps(null);
            return;
        }        // If checks pass, proceed with Thirdweb writeContract
        try {
            writeContract({
                address: carmarketplace_address as Address,
                abi: carmarketplace_abi,
                functionName: 'cancelListing',
                args: [cancelProps.tokenId],
                onSuccess: (txHash) => {
                    console.log('Cancel listing transaction sent:', txHash);
                    if (cancelProps.onSuccess) cancelProps.onSuccess(txHash);
                },
                onError: (err) => {
                    console.error('Cancel listing write error:', err);
                    setError(err);
                    setIsCancelling(false);
                    if (cancelProps.onError) cancelProps.onError(err);
                    setCancelProps(null);
                },
            });
        } catch (err) {
            console.error('Write contract error:', err);
            setError(err as Error);
            setIsCancelling(false);
            if (cancelProps.onError) cancelProps.onError(err as Error);
            setCancelProps(null);
        }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isCancelling, cancelProps, listingDetails, ownerOfToken, isLoadingListing, isLoadingOwner, connectedAddress]);

    // Reset isCancelling state when transaction is confirmed or fails
    useEffect(() => {
        if (isConfirmed || (error && !hash)) {
            setIsCancelling(false);
            setCancelProps(null);
        }
    }, [isConfirmed, error, hash]);

    const clearError = () => setError(null);

    return {
        cancelListing,
        isCancelling: isCancelling || isConfirming,
        isConfirming,
        isConfirmed,
        hash,
        error,
        clearError
    };
}

// --- Hook for USDT Approval (Hybrid: Thirdweb writes + Wagmi reads) ---
export function useApproveUsdtHybrid(amount: bigint) {
    const { address: connectedAddress } = useThirdwebWagmiAccount();
    const [isApproving, setIsApproving] = useState(false);
    const [approvalError, setApprovalError] = useState<Error | null>(null);
    const { writeContract, hash } = useThirdwebWriteContract();
    const { isLoading: isConfirmingApproval, isSuccess: isApprovalConfirmed } = useThirdwebWaitForTransaction(hash);

    // Read current allowance using Wagmi
    const { data: currentAllowanceData, refetch: refetchAllowance, isLoading: isLoadingAllowanceStatus } = useReadContract({
        address: usdt_address as Address,
        abi: usdt_abi,
        functionName: 'allowance',
        args: connectedAddress ? [connectedAddress, carmarketplace_address as Address] : undefined,
        query: { enabled: !!connectedAddress }
    });
    const currentAllowance = typeof currentAllowanceData === 'bigint' ? currentAllowanceData : BigInt(0);
    const needsApproval = currentAllowance < amount;

    const approveUsdt = async () => {
        if (!connectedAddress) {
            const err = new Error('Wallet not connected');
            setApprovalError(err);
            return;
        }
        
        setIsApproving(true);
        setApprovalError(null);

        writeContract({
            address: usdt_address as Address,
            abi: usdt_abi,
            functionName: 'approve',
            args: [carmarketplace_address as Address, amount],
            onSuccess: (txHash) => {
                console.log('USDT approval transaction sent:', txHash);
            },
            onError: (err) => {
                console.error('USDT approval error:', err);
                setApprovalError(err);
                setIsApproving(false);
            },
        });
    };

    // Reset state when transaction is confirmed
    useEffect(() => {
        if (isApprovalConfirmed) {
            setIsApproving(false);
            refetchAllowance(); // Refresh allowance after approval
        }
    }, [isApprovalConfirmed, refetchAllowance]);

    const clearApprovalError = () => setApprovalError(null);

    return {
        approveUsdt,
        needsApproval,
        isApproving: isApproving || isConfirmingApproval,
        isConfirmingApproval,
        isApprovalConfirmed,
        approvalError,
        clearApprovalError,
        isLoadingAllowanceStatus,
        currentAllowance,
        refetchAllowance
    };
}

// --- Hook for NFT Approval (Hybrid: Thirdweb writes + Wagmi reads) ---
export function useApproveMarketplaceHybrid(tokenId?: bigint) {
    const { address: connectedAddress } = useThirdwebWagmiAccount();
    const [isApproving, setIsApproving] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const { writeContract, hash } = useThirdwebWriteContract();
    const { isLoading: isConfirming, isSuccess: isConfirmed } = useThirdwebWaitForTransaction(hash);

    // Read current approval status using Wagmi
    const { data: approvedAddressData, refetch: refetchApproved, isLoading: isLoadingApproved } = useReadContract({
        address: carnft_address as Address,
        abi: carnft_abi,
        functionName: 'getApproved',
        args: tokenId ? [tokenId] : undefined,
        query: { enabled: !!tokenId && !!connectedAddress }
    });
    const approvedAddress = approvedAddressData as Address | undefined;
    const needsApproval = !!tokenId && !!connectedAddress && !isLoadingApproved && approvedAddress?.toLowerCase() !== carmarketplace_address.toLowerCase();

    const approveMarketplace = async () => {
        if (!connectedAddress) {
            setError(new Error('Wallet not connected'));
            return;
        }
        if (!tokenId) {
            setError(new Error('Token ID not specified for approval'));
            return;
        }
        if (!needsApproval) {
            console.log("Approval not needed or already granted.");
            return;
        }

        setIsApproving(true);
        setError(null);

        writeContract({
            address: carnft_address as Address,
            abi: carnft_abi,
            functionName: 'approve',
            args: [carmarketplace_address as Address, tokenId],
            onSuccess: (txHash) => {
                console.log('Approve marketplace transaction sent:', txHash);
            },
            onError: (err) => {
                console.error('Approve marketplace write error:', err);
                setError(err);
                setIsApproving(false);
            },
        });
    };

    // Effect to refetch approval status after confirmation
    useEffect(() => {
        if (isConfirmed) {
            refetchApproved();
            setIsApproving(false);
        }
        if (error && !hash) {
            setIsApproving(false);
        }
    }, [isConfirmed, error, hash, refetchApproved]);

    const clearError = () => setError(null);

    return {
        approveMarketplace,
        needsApproval,
        isApproving: isApproving || isConfirming,
        isConfirmingApproval: isConfirming,
        isApproved: isConfirmed,
        approvalHash: hash,
        approvalError: error,
        clearApprovalError: clearError,
        isLoadingApprovalStatus: isLoadingApproved,
    };
}

// Export the read-only hooks from the original file (these don't need modification as they use Wagmi)
export { useActiveListings, useListingDetails } from './useCarMarketplace';
