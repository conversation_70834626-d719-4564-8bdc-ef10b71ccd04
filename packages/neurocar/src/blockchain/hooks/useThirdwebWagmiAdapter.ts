"use client";

/* eslint-disable @typescript-eslint/no-explicit-any */
// Disable any type restrictions for this adapter file due to Thirdweb-Wagmi type compatibility

import { useState, useEffect } from 'react';
import { useActiveAccount, useSendTransaction } from 'thirdweb/react';
import { type Address, type Hash } from 'viem';
import { prepareContractCall, getContract } from 'thirdweb';
import { client, avalancheFuji } from '@/app/client';

// Adapter to make Thirdweb work with Wagmi-like interface for account
export function useThirdwebWagmiAccount() {
  const account = useActiveAccount();
  
  return {
    address: account?.address as Address | undefined,
    isConnected: !!account,
    isConnecting: false, // Thirdweb handles this internally
    isDisconnected: !account,
  };
}

// Adapter for contract writes using Thirdweb
export function useThirdwebWriteContract() {
  const { mutate: sendTransaction } = useSendTransaction();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hash, setHash] = useState<Hash | undefined>();
  const writeContract = async ({
    address,
    abi,
    functionName,
    args = [],
    onSuccess,
    onError,
  }: {
    address: Address;
    abi: any; // Use any to avoid ABI type conflicts between Wagmi and Thirdweb
    functionName: string;
    args?: unknown[];
    onSuccess?: (hash: Hash) => void;
    onError?: (error: Error) => void;
  }) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use getContract to properly handle ABI types
      const contract = getContract({
        client,
        chain: avalancheFuji,
        address: address as `0x${string}`,
        abi: abi as any, // Type assertion to bypass strict ABI typing
      } as any); // Full type assertion for the contract object

      const transaction = prepareContractCall({
        contract: contract as any, // Type assertion for prepareContractCall
        method: functionName,
        params: args,
      });

      (sendTransaction as any)(transaction, {
        onSuccess: (result: any) => {
          setHash(result.transactionHash as Hash);
          setIsLoading(false);
          if (onSuccess) onSuccess(result.transactionHash as Hash);
        },
        onError: (error: any) => {
          const err = error instanceof Error ? error : new Error('Transaction failed');
          setError(err);
          setIsLoading(false);
          if (onError) onError(err);
        },
      });
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to prepare transaction');
      setError(err);
      setIsLoading(false);
      if (onError) onError(err);
    }
  };

  const clearError = () => setError(null);

  return {
    writeContract,
    isLoading,
    error,
    hash,
    clearError,
  };
}

// Adapter for transaction waiting using Thirdweb
export function useThirdwebWaitForTransaction(hash?: Hash) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!hash) return;

    setIsLoading(true);
    setIsSuccess(false);
    setError(null);

    // Simple simulation of transaction confirmation
    // In a real implementation, you'd want to use Thirdweb's transaction tracking
    const timeout = setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
    }, 3000); // Simulate 3 second confirmation

    return () => clearTimeout(timeout);
  }, [hash]);

  return {
    isLoading,
    isSuccess,
    error,
  };
}
