import { useState, useEffect } from 'react';
import { CarDetailsService } from '@/services/carDetailsService';
import { CarDetailsWeb2, CarImage, MarketplaceListing } from '@/types/supabase';

export function useCarDetailsWeb2(tokenId?: string) {
  const [details, setDetails] = useState<CarDetailsWeb2 | null>(null);
  const [images, setImages] = useState<CarImage[]>([]);
  const [listing, setListing] = useState<MarketplaceListing | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  const fetchCarDetails = async () => {
    if (!tokenId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await CarDetailsService.getEnhancedCarDetails(tokenId);
      
      if (result.error) {
        setError(result.error);
      } else {
        setDetails(result.details);
        setImages(result.images);
        setListing(result.listing);
      }
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCarDetails();
  }, [tokenId]);

  const updateDetails = async (updates: Partial<CarDetailsWeb2>) => {
    if (!tokenId) return { success: false, error: 'No token ID provided' };

    try {
      const result = await CarDetailsService.updateCarDetails(tokenId, updates);
      if (result.error) {
        return { success: false, error: result.error };
      }
      
      setDetails(result.data);
      return { success: true, data: result.data };
    } catch (err) {
      return { success: false, error: err };
    }
  };

  const addImage = async (imageData: Omit<CarImage, 'id' | 'created_at'>) => {
    try {
      const result = await CarDetailsService.addCarImage(imageData);
      if (result.error) {
        return { success: false, error: result.error };
      }
      
      setImages(prev => [...prev, result.data!]);
      return { success: true, data: result.data };
    } catch (err) {
      return { success: false, error: err };
    }
  };

  const removeImage = async (imageId: string) => {
    try {
      const result = await CarDetailsService.deleteCarImage(imageId);
      if (result.error) {
        return { success: false, error: result.error };
      }
      
      setImages(prev => prev.filter(img => img.id !== imageId));
      return { success: true };
    } catch (err) {
      return { success: false, error: err };
    }
  };

  const createOrUpdateListing = async (listingData: Partial<MarketplaceListing>) => {
    if (!tokenId) return { success: false, error: 'No token ID provided' };

    try {
      let result;
      if (listing) {
        result = await CarDetailsService.updateMarketplaceListing(tokenId, listingData);
      } else {
        result = await CarDetailsService.createMarketplaceListing({
          token_id: tokenId,
          seller_address: '',
          price_usdt: '0',
          listing_description: '',
          is_active: true,
          featured: false,
          views_count: 0,
          ...listingData
        } as any);
      }

      if (result.error) {
        return { success: false, error: result.error };
      }
      
      setListing(result.data);
      return { success: true, data: result.data };
    } catch (err) {
      return { success: false, error: err };
    }
  };

  return {
    details,
    images,
    listing,
    loading,
    error,
    refetch: fetchCarDetails,
    updateDetails,
    addImage,
    removeImage,
    createOrUpdateListing
  };
}

export function useActiveListings() {
  const [listings, setListings] = useState<MarketplaceListing[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  const fetchListings = async (limit: number = 20, offset: number = 0) => {
    setLoading(true);
    setError(null);

    try {
      const result = await CarDetailsService.getActiveListings(limit, offset);
      
      if (result.error) {
        setError(result.error);
      } else {
        setListings(result.data);
      }
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListings();
  }, []);

  return {
    listings,
    loading,
    error,
    refetch: fetchListings
  };
}
