-- Create car_details_web2 table for additional car information
CREATE TABLE IF NOT EXISTS car_details_web2 (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token_id TEXT NOT NULL UNIQUE,
    additional_images TEXT[] DEFAULT '{}',
    description TEXT DEFAULT '',
    features TEXT[] DEFAULT '{}',
    condition_report TEXT DEFAULT '',
    service_history TEXT DEFAULT '',
    accident_history TEXT DEFAULT '',
    modifications TEXT[] DEFAULT '{}',
    seller_notes TEXT DEFAULT '',
    location TEXT DEFAULT '',
    contact_info TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create car_images table for storing multiple images per car
CREATE TABLE IF NOT EXISTS car_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token_id TEXT NOT NULL,
    image_url TEXT NOT NULL,
    image_type TEXT CHECK (image_type IN ('exterior', 'interior', 'engine', 'documents', 'other')) DEFAULT 'other',
    description TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create marketplace_listings table for enhanced marketplace functionality
CREATE TABLE IF NOT EXISTS marketplace_listings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token_id TEXT NOT NULL UNIQUE,
    seller_address TEXT NOT NULL,
    price_usdt TEXT NOT NULL,
    listing_description TEXT DEFAULT '',
    is_active BOOLEAN DEFAULT TRUE,
    featured BOOLEAN DEFAULT FALSE,
    views_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create offers table for the offer system
CREATE TABLE IF NOT EXISTS offers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token_id TEXT NOT NULL,
    buyer_address TEXT NOT NULL,
    seller_address TEXT NOT NULL,
    offer_amount TEXT NOT NULL,
    message TEXT DEFAULT '',
    status TEXT CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn', 'completed')) DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_car_details_token_id ON car_details_web2(token_id);
CREATE INDEX IF NOT EXISTS idx_car_images_token_id ON car_images(token_id);
CREATE INDEX IF NOT EXISTS idx_car_images_is_primary ON car_images(is_primary);
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_token_id ON marketplace_listings(token_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_active ON marketplace_listings(is_active);
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_featured ON marketplace_listings(featured);
CREATE INDEX IF NOT EXISTS idx_offers_token_id ON offers(token_id);
CREATE INDEX IF NOT EXISTS idx_offers_buyer_address ON offers(buyer_address);
CREATE INDEX IF NOT EXISTS idx_offers_seller_address ON offers(seller_address);
CREATE INDEX IF NOT EXISTS idx_offers_status ON offers(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_car_details_web2_updated_at 
    BEFORE UPDATE ON car_details_web2 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_marketplace_listings_updated_at 
    BEFORE UPDATE ON marketplace_listings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment listing views
CREATE OR REPLACE FUNCTION increment_listing_views(listing_token_id TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE marketplace_listings 
    SET views_count = views_count + 1 
    WHERE token_id = listing_token_id AND is_active = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security (RLS)
ALTER TABLE car_details_web2 ENABLE ROW LEVEL SECURITY;
ALTER TABLE car_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access and authenticated write access
-- Note: Adjust these policies based on your specific security requirements

-- Car details policies
CREATE POLICY "Allow public read access to car details" ON car_details_web2
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert car details" ON car_details_web2
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update car details" ON car_details_web2
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Car images policies
CREATE POLICY "Allow public read access to car images" ON car_images
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert car images" ON car_images
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update car images" ON car_images
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to delete car images" ON car_images
    FOR DELETE USING (auth.role() = 'authenticated');

-- Marketplace listings policies
CREATE POLICY "Allow public read access to marketplace listings" ON marketplace_listings
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert marketplace listings" ON marketplace_listings
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update marketplace listings" ON marketplace_listings
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Offers policies
CREATE POLICY "Allow public read access to offers" ON offers
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert offers" ON offers
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update offers" ON offers
    FOR UPDATE USING (auth.role() = 'authenticated');
