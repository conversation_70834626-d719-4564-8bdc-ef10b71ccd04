# Thirdweb + Wagmi Integration Guide

This guide shows how to integrate Thirdweb for authentication and wallet connection while keeping Wagmi for contract interactions.

## Overview

- **Thirdweb**: Handles wallet connection, authentication, and transaction signing
- **Wagmi**: Handles contract reads and provides excellent caching/state management
- **Adapter**: Bridges the gap between both libraries with a Wagmi-like interface

## Setup

### 1. Provider Configuration

```tsx
// src/app/providers.tsx
"use client";

import { ThirdwebProvider } from "thirdweb/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiProvider } from "wagmi";
import { config } from "@/blockchain/config/wagmi";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 30_000,
    },
  },
});

export const Providers = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThirdwebProvider>
        <WagmiProvider config={config}>
          {children}
        </WagmiProvider>
      </ThirdwebProvider>
    </QueryClientProvider>
  );
};
```

### 2. Wallet Connection Component

```tsx
// Using Thirdweb's ConnectButton
import { ConnectButton } from "thirdweb/react";
import { client, wallets, avalancheFuji } from "@/app/client";

function WalletConnection() {
  return (
    <ConnectButton
      client={client}
      wallets={wallets}
      chain={avalancheFuji}
      connectModal={{
        size: "wide",
        title: "Connect to Neurocar",
        showThirdwebBranding: false,
      }}
    />
  );
}
```

## Usage Patterns

### 1. Getting Wallet Information

```tsx
// Old (Wagmi only)
import { useAccount } from 'wagmi';
const { address, isConnected } = useAccount();

// New (Thirdweb)
import { useThirdwebWagmiAccount } from '@/hooks/useThirdwebWagmiAdapter';
const { address, isConnected } = useThirdwebWagmiAccount();
```

### 2. Writing to Contracts

```tsx
// Old (Wagmi)
import { useWriteContract } from 'wagmi';

const { writeContract } = useWriteContract();

writeContract({
  address: contractAddress,
  abi: contractAbi,
  functionName: 'mintCar',
  args: [to, vin, make, model, year, registration, imageURI],
});

// New (Thirdweb via Adapter)
import { useThirdwebWriteContract } from '@/hooks/useThirdwebWagmiAdapter';

const { writeContract } = useThirdwebWriteContract();

await writeContract({
  address: contractAddress,
  abi: contractAbi,
  functionName: 'mintCar',
  args: [to, vin, make, model, year, registration, imageURI],
  onSuccess: (hash) => console.log('Success:', hash),
  onError: (error) => console.error('Error:', error),
});
```

### 3. Reading from Contracts (No Change!)

```tsx
// Keep using Wagmi for reads - they work perfectly!
import { useContractRead } from '@/hooks/useContractReads';

const { data: carDetails } = useContractRead({
  address: carnft_address,
  abi: carnft_abi,
  functionName: 'getCarDetails',
  args: [tokenId],
  enabled: !!tokenId,
});
```

## Migration Steps

### Step 1: Update Hook Imports

```tsx
// Before
import { useAccount, useWriteContract } from 'wagmi';

// After
import { useThirdwebWagmiAccount, useThirdwebWriteContract } from '@/hooks/useThirdwebWagmiAdapter';
// Keep wagmi for reads
import { useContractRead } from '@/hooks/useContractReads';
```

### Step 2: Update Write Operations

```tsx
// Before
const { writeContract, isPending } = useWriteContract();

// After
const { writeContract, isLoading } = useThirdwebWriteContract();
```

### Step 3: Update Transaction Handling

```tsx
// Before
writeContract({
  address,
  abi,
  functionName,
  args,
}, {
  onSuccess: (hash) => {},
  onError: (error) => {},
});

// After
await writeContract({
  address,
  abi,
  functionName,
  args,
  onSuccess: (hash) => {},
  onError: (error) => {},
});
```

## Example: Complete Hook Migration

### Before (Wagmi only)

```tsx
export function useAddMaintenanceRecord() {
  const { address } = useAccount();
  const { writeContract } = useWriteContract();
  const { isLoading, isSuccess } = useWaitForTransactionReceipt({ hash });

  const addRecord = async (params) => {
    writeContract({
      address: carnft_address,
      abi: carnft_abi,
      functionName: 'addMaintenanceRecord',
      args: [params.tokenId, params.description, /* ... */],
    });
  };

  return { addRecord, isLoading, isSuccess };
}
```

### After (Thirdweb + Wagmi)

```tsx
export function useAddMaintenanceRecord() {
  const { address } = useThirdwebWagmiAccount(); // Thirdweb for auth
  const { writeContract } = useThirdwebWriteContract(); // Thirdweb for writes
  const { isLoading, isSuccess } = useThirdwebWaitForTransaction(hash); // Thirdweb for tx waiting

  const addRecord = async (params) => {
    await writeContract({
      address: carnft_address,
      abi: carnft_abi,
      functionName: 'addMaintenanceRecord',
      args: [params.tokenId, params.description, /* ... */],
      onSuccess: params.onSuccess,
      onError: params.onError,
    });
  };

  return { addRecord, isLoading, isSuccess };
}
```

## Benefits

1. **Better UX**: Thirdweb provides social login, email auth, passkeys
2. **Reliable Contracts**: Wagmi's proven contract interaction patterns
3. **Type Safety**: Full TypeScript support throughout
4. **Easy Migration**: Minimal changes to existing code
5. **Best of Both**: Combine the strengths of both libraries

## Common Patterns

### Pattern 1: Component with Auth + Contract Interaction

```tsx
function CarMintingForm() {
  // Get wallet info from Thirdweb
  const { address, isConnected } = useThirdwebWagmiAccount();
  
  // Use hybrid hooks for contract operations
  const { mintCar, isMinting } = useMintCar();
  
  // Read data still works with Wagmi
  const { data: userCars } = useUserCars(address);

  const handleMint = async () => {
    if (!isConnected) return;
    
    await mintCar({
      to: address,
      vin: "1HGBH41JXMN109186",
      make: "Toyota",
      model: "Camry",
      year: BigInt(2023),
      registrationNumber: "ABC-123",
      imageURI: "ipfs://...",
    });
  };

  return (
    <div>
      {!isConnected ? (
        <ConnectButton client={client} wallets={wallets} />
      ) : (
        <button onClick={handleMint} disabled={isMinting}>
          {isMinting ? 'Minting...' : 'Mint Car'}
        </button>
      )}
    </div>
  );
}
```

### Pattern 2: Error Handling

```tsx
const { writeContract, error } = useThirdwebWriteContract();

const handleTransaction = async () => {
  try {
    await writeContract({
      address,
      abi,
      functionName,
      args,
      onSuccess: (hash) => {
        toast.success(`Transaction successful: ${hash}`);
      },
      onError: (error) => {
        toast.error(`Transaction failed: ${error.message}`);
      },
    });
  } catch (error) {
    console.error('Transaction error:', error);
  }
};
```

## Troubleshooting

### Issue: "Wallet not connected"
**Solution**: Make sure you're using `useThirdwebWagmiAccount` instead of `useAccount`

### Issue: Type errors with contract calls
**Solution**: Ensure your ABI types are properly imported and contract addresses are cast to `Address` type

### Issue: Transaction not confirming
**Solution**: Use `useThirdwebWaitForTransaction` with the transaction hash for proper confirmation handling

## File Structure

```
src/
├── app/
│   ├── client.ts              # Thirdweb client config
│   ├── providers.tsx          # Both providers setup
│   └── demo/
│       └── page.tsx           # Demo component
├── blockchain/
│   └── hooks/
│       ├── useThirdwebWagmiAdapter.ts    # Bridge between libraries
│       ├── useCarNFTHybrid.ts            # Updated hooks using adapter
│       └── useContractReads.ts           # Keep existing read hooks
```

This integration gives you the best of both worlds: Thirdweb's superior authentication and user experience combined with Wagmi's robust contract interaction capabilities.
