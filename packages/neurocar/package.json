{"name": "neurocar", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ethersproject/providers": "^5.8.0", "@google/generative-ai": "^0.2.1", "@headlessui/react": "^2.2.0", "@langchain/community": "^0.0.27", "@langchain/core": "^0.1.63", "@langchain/google-genai": "^0.0.7", "@rainbow-me/rainbowkit": "^2.0.5", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.70.0", "@types/leaflet": "^1.9.17", "@web3-storage/w3up-client": "^17.2.0", "axios": "^1.8.4", "dotenv": "^16.4.7", "ethers": "^6.13.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "langchain": "^0.3.19", "leaflet": "^1.9.4", "lucide-react": "^0.484.0", "next": "15.2.4", "openai": "^4.90.0", "pinata": "^2.1.3", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-qr-code": "^2.0.15", "sonner": "^2.0.3", "tailwindcss-animate": "^1.0.7", "thirdweb": "^5.93.6", "viem": "^2.24.1", "wagmi": "^2.14.15", "web3.storage": "^4.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "pino-pretty": "^13.0.0", "tailwindcss": "^4", "typescript": "^5"}}