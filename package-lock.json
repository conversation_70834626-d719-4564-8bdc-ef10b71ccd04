{"name": "carlogix", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"packages/hardhat": {"version": "1.0.0", "extraneous": true, "license": "MIT", "dependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-ignition": "^0.15.0", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@nomicfoundation/ignition-core": "^0.15.0", "@openzeppelin/contracts": "^5.2.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "chai": "^4.2.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.1", "ts-node": ">=8.0.0", "typechain": "^8.3.0", "typescript": ">=4.5.0"}, "devDependencies": {"@hashgraph/sdk": "^2.61.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "dotenv": "^16.4.7", "hardhat": "^2.22.19"}}, "packages/neurocar": {"version": "0.1.0", "extraneous": true, "dependencies": {"next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}}}