# Neurocar

1. CarNFT Contract
   This contract represents cars as NFTs on the blockchain with:

Detailed car information (make, model, year, VIN)
Vehicle images through IPFS metadata
Complete maintenance history
Issue tracking and resolution
Ownership verification

Key features:

Vehicle minting with images and metadata
Maintenance record tracking with documentation
Issue reporting with evidence images
Vehicle history verification

2. CarInsurancePool Contract with Assessors
   This contract manages the decentralized insurance system with:

Certified professional assessors for claim validation
Community oversight through dispute mechanisms
Transparent pool management
Automatic claim processing

Key insurance processes:

Claim Submission

Car owners submit claims with evidence
Claims are assigned to certified assessors

Professional Assessment

Assessors evaluate claims and recommend payouts
Assessors provide detailed evaluations with evidence

Community Oversight

Pool members can dispute assessor decisions
If enough members dispute, a community vote is triggered

Democratic Resolution

Community votes can override assessor decisions
Transparent voting ensures fair claim resolutions

Automatic Payouts

Approved claims are automatically paid from the pool

These contracts create a hybrid system that balances professional assessment with community oversight, providing both expertise and democratic accountability in the insurance process.
